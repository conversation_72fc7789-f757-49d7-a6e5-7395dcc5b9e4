import { Injectable, NgZone, PLA<PERSON>ORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Observable, Subject, BehaviorSubject } from 'rxjs';

export interface SSEEvent {
  type: string;
  data: any;
  id?: string;
  retry?: number;
}

export interface SSEConnectionStatus {
  connected: boolean;
  error?: string;
  lastEventTime?: Date;
}

@Injectable({
  providedIn: 'root'
})
export class SSEClientService {
  private eventSource: EventSource | null = null;
  private eventsSubject = new Subject<SSEEvent>();
  private connectionStatusSubject = new BehaviorSubject<SSEConnectionStatus>({ connected: false });
  
  public events$ = this.eventsSubject.asObservable();
  public connectionStatus$ = this.connectionStatusSubject.asObservable();

  constructor(
    private ngZone: NgZone,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  /**
   * Connect to SSE endpoint
   */
  connect(url: string, options?: EventSourceInit): Observable<SSEEvent> {
    // Only connect in browser environment
    if (!isPlatformBrowser(this.platformId)) {
      console.warn('SSE not available in server environment');
      this.connectionStatusSubject.next({
        connected: false,
        error: 'SSE not available in server environment'
      });
      return this.events$;
    }

    this.disconnect(); // Close any existing connection

    try {
      this.eventSource = new EventSource(url, options);
      
      this.eventSource.onopen = (event) => {
        this.ngZone.run(() => {
          console.log('SSE connection opened:', event);
          this.connectionStatusSubject.next({ 
            connected: true, 
            lastEventTime: new Date() 
          });
        });
      };

      this.eventSource.onmessage = (event) => {
        this.ngZone.run(() => {
          try {
            const data = event.data ? JSON.parse(event.data) : null;
            const sseEvent: SSEEvent = {
              type: 'message',
              data: data,
              id: event.lastEventId
            };
            
            this.eventsSubject.next(sseEvent);
            this.connectionStatusSubject.next({ 
              connected: true, 
              lastEventTime: new Date() 
            });
          } catch (error) {
            console.error('Error parsing SSE message:', error);
            this.eventsSubject.next({
              type: 'error',
              data: { error: 'Failed to parse message', raw: event.data }
            });
          }
        });
      };

      this.eventSource.onerror = (event) => {
        this.ngZone.run(() => {
          console.error('SSE connection error:', event);
          const errorMessage = this.eventSource?.readyState === EventSource.CLOSED 
            ? 'Connection closed' 
            : 'Connection error';
          
          this.connectionStatusSubject.next({ 
            connected: false, 
            error: errorMessage 
          });
          
          this.eventsSubject.next({
            type: 'error',
            data: { error: errorMessage, event }
          });
        });
      };

      // Listen for custom event types
      this.setupCustomEventListeners();

    } catch (error) {
      console.error('Failed to create SSE connection:', error);
      this.connectionStatusSubject.next({ 
        connected: false, 
        error: 'Failed to create connection' 
      });
    }

    return this.events$;
  }

  /**
   * Disconnect from SSE endpoint
   */
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
      this.connectionStatusSubject.next({ connected: false });
      console.log('SSE connection closed');
    }
  }

  /**
   * Check if currently connected
   */
  isConnected(): boolean {
    return this.eventSource?.readyState === EventSource.OPEN;
  }

  /**
   * Get current connection state
   */
  getReadyState(): number | null {
    return this.eventSource?.readyState || null;
  }

  /**
   * Setup listeners for custom event types
   */
  private setupCustomEventListeners(): void {
    if (!this.eventSource) return;

    // Listen for chat-specific events
    const chatEventTypes = [
      'chat-message',
      'chat-response',
      'chat-error',
      'model-update',
      'query-result',
      'connection-status'
    ];

    chatEventTypes.forEach(eventType => {
      this.eventSource!.addEventListener(eventType, (event: any) => {
        this.ngZone.run(() => {
          try {
            const data = event.data ? JSON.parse(event.data) : null;
            const sseEvent: SSEEvent = {
              type: eventType,
              data: data,
              id: event.lastEventId
            };
            
            this.eventsSubject.next(sseEvent);
            this.connectionStatusSubject.next({ 
              connected: true, 
              lastEventTime: new Date() 
            });
          } catch (error) {
            console.error(`Error parsing ${eventType} event:`, error);
            this.eventsSubject.next({
              type: 'error',
              data: { error: `Failed to parse ${eventType}`, raw: event.data }
            });
          }
        });
      });
    });
  }

  /**
   * Filter events by type
   */
  getEventsByType(eventType: string): Observable<SSEEvent> {
    return new Observable(observer => {
      const subscription = this.events$.subscribe(event => {
        if (event.type === eventType) {
          observer.next(event);
        }
      });
      
      return () => subscription.unsubscribe();
    });
  }

  /**
   * Reconnect with exponential backoff
   */
  reconnect(url: string, options?: EventSourceInit, maxRetries: number = 5): void {
    let retryCount = 0;
    
    const attemptReconnect = () => {
      if (retryCount >= maxRetries) {
        console.error('Max reconnection attempts reached');
        this.connectionStatusSubject.next({ 
          connected: false, 
          error: 'Max reconnection attempts reached' 
        });
        return;
      }

      const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
      console.log(`Attempting to reconnect in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`);
      
      setTimeout(() => {
        retryCount++;
        this.connect(url, options).subscribe({
          error: () => {
            if (retryCount < maxRetries) {
              attemptReconnect();
            }
          }
        });
      }, delay);
    };

    attemptReconnect();
  }

  /**
   * Cleanup on service destruction
   */
  ngOnDestroy(): void {
    this.disconnect();
    this.eventsSubject.complete();
    this.connectionStatusSubject.complete();
  }
}
