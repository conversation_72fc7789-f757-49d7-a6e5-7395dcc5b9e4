import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export interface MindsDBConfig {
  baseUrl: string;
  username?: string;
  password?: string;
  database?: string;
}

export interface QueryResult {
  data: any[];
  columns: string[];
  error?: string;
}

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class MindsDBService {
  private config: MindsDBConfig = {
    baseUrl: 'http://localhost:47334',
    database: 'mindsdb'
  };

  private isConnectedSubject = new BehaviorSubject<boolean>(false);
  public isConnected$ = this.isConnectedSubject.asObservable();

  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    })
  };

  constructor(
    private http: HttpClient,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  /**
   * Configure MindsDB connection settings
   */
  configure(config: Partial<MindsDBConfig>): void {
    this.config = { ...this.config, ...config };
    this.updateHttpOptions();
  }

  /**
   * Test connection to MindsDB
   */
  testConnection(): Observable<boolean> {
    // Skip connection test in SSR
    if (!isPlatformBrowser(this.platformId)) {
      this.isConnectedSubject.next(false);
      return of(false);
    }

    return this.http.get(`${this.config.baseUrl}/api/status`, this.httpOptions)
      .pipe(
        map(() => {
          this.isConnectedSubject.next(true);
          return true;
        }),
        catchError(error => {
          console.error('MindsDB connection failed:', error);
          this.isConnectedSubject.next(false);
          return throwError(() => error);
        })
      );
  }

  /**
   * Execute SQL query against MindsDB
   */
  executeQuery(query: string): Observable<QueryResult> {
    const payload = {
      query: query,
      context: {
        database: this.config.database
      }
    };

    return this.http.post<any>(`${this.config.baseUrl}/api/sql/query`, payload, this.httpOptions)
      .pipe(
        map(response => ({
          data: response.data || [],
          columns: response.column_names || [],
          error: response.error_message
        })),
        catchError(error => {
          console.error('Query execution failed:', error);
          return throwError(() => error);
        })
      );
  }

  /**
   * Get available models from MindsDB
   */
  getModels(): Observable<string[]> {
    const query = "SELECT name FROM mindsdb.models WHERE active = 1";
    return this.executeQuery(query).pipe(
      map(result => result.data.map(row => row.name || row[0]))
    );
  }

  /**
   * Create a new model in MindsDB
   */
  createModel(modelName: string, modelConfig: any): Observable<QueryResult> {
    const query = `
      CREATE MODEL ${modelName}
      PREDICT target
      USING
        engine = '${modelConfig.engine || 'openai'}',
        ${Object.entries(modelConfig.parameters || {})
          .map(([key, value]) => `${key} = '${value}'`)
          .join(',\n        ')}
    `;
    return this.executeQuery(query);
  }

  /**
   * Query a model for chat completion
   */
  queryModel(modelName: string, prompt: string, context?: any): Observable<string> {
    const contextStr = context ? JSON.stringify(context) : '{}';
    const query = `
      SELECT response
      FROM ${modelName}
      WHERE text = '${prompt.replace(/'/g, "''")}'
      AND context = '${contextStr}'
    `;

    return this.executeQuery(query).pipe(
      map(result => {
        if (result.error) {
          throw new Error(result.error);
        }
        return result.data[0]?.response || result.data[0]?.[0] || 'No response received';
      })
    );
  }

  /**
   * Get chat history from MindsDB (if stored in a table)
   */
  getChatHistory(conversationId?: string): Observable<ChatMessage[]> {
    let query = "SELECT * FROM chat_history ORDER BY timestamp DESC LIMIT 50";
    if (conversationId) {
      query = `SELECT * FROM chat_history WHERE conversation_id = '${conversationId}' ORDER BY timestamp DESC LIMIT 50`;
    }

    return this.executeQuery(query).pipe(
      map(result => 
        result.data.map(row => ({
          id: row.id || row[0],
          content: row.content || row[1],
          role: row.role || row[2],
          timestamp: new Date(row.timestamp || row[3])
        }))
      ),
      catchError(() => {
        // If chat_history table doesn't exist, return empty array
        return [];
      })
    );
  }

  /**
   * Save chat message to MindsDB
   */
  saveChatMessage(message: ChatMessage, conversationId?: string): Observable<QueryResult> {
    const query = `
      INSERT INTO chat_history (id, content, role, timestamp, conversation_id)
      VALUES ('${message.id}', '${message.content.replace(/'/g, "''")}', '${message.role}', 
              '${message.timestamp.toISOString()}', '${conversationId || 'default'}')
    `;
    return this.executeQuery(query);
  }

  private updateHttpOptions(): void {
    const headers: any = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    if (this.config.username && this.config.password) {
      const auth = btoa(`${this.config.username}:${this.config.password}`);
      headers['Authorization'] = `Basic ${auth}`;
    }

    this.httpOptions = { headers: new HttpHeaders(headers) };
  }
}
