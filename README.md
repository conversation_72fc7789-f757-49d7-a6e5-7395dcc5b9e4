# MindsDB Angular Chat Application

A modern, real-time chat application built with Angular that integrates with MindsDB for AI-powered conversations. Features Server-Sent Events (SSE) for real-time communication and a responsive chat interface.

## Features

- 🤖 **AI-Powered Chat**: Integrate with MindsDB models for intelligent conversations
- ⚡ **Real-time Communication**: Server-Sent Events for instant message delivery
- 💬 **Multiple Conversations**: Create and manage multiple chat sessions
- 🎨 **Modern UI**: Clean, responsive design with dark sidebar and light chat area
- 🔄 **Auto-reconnection**: Automatic reconnection for SSE connections
- 📱 **Mobile Responsive**: Works seamlessly on desktop and mobile devices
- 🔧 **Configurable**: Easy environment configuration for different deployments

## Prerequisites

- Node.js (v18 or higher)
- Angular CLI (v19 or higher)
- MindsDB instance running (local or cloud)

## Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd ang-sse-client
   npm install
   ```

2. **Configure MindsDB Connection**

   Edit `src/environments/environment.ts`:
   ```typescript
   export const environment = {
     production: false,
     mindsdb: {
       baseUrl: 'http://localhost:47334', // Your MindsDB URL
       username: '', // Optional: MindsDB username
       password: '', // Optional: MindsDB password
       database: 'mindsdb'
     },
     sse: {
       baseUrl: 'http://localhost:47334',
       endpoint: '/api/sse/chat'
     },
     chat: {
       defaultModel: 'gpt-4', // Your preferred model
       maxMessageLength: 4000,
       enableHistory: true,
       autoSave: true
     }
   };
   ```

3. **Start the Application**
   ```bash
   ng serve
   ```

   Navigate to `http://localhost:4200/`

## MindsDB Setup

### Local MindsDB Installation

1. **Install MindsDB**
   ```bash
   pip install mindsdb
   ```

2. **Start MindsDB**
   ```bash
   python -m mindsdb
   ```

   MindsDB will be available at `http://localhost:47334`

3. **Create a Model** (Example with OpenAI)
   ```sql
   CREATE MODEL gpt4_model
   PREDICT response
   USING
     engine = 'openai',
     api_key = 'your-openai-api-key',
     model_name = 'gpt-4',
     prompt_template = 'Answer the user question: {{text}}';
   ```

### Cloud MindsDB

1. Sign up at [MindsDB Cloud](https://cloud.mindsdb.com/)
2. Update the `baseUrl` in environment configuration
3. Add authentication credentials if required

## Architecture

### Services

- **MindsDBService**: Handles API communication with MindsDB
- **SSEClientService**: Manages Server-Sent Events connections
- **ChatService**: Orchestrates chat functionality and state management

### Components

- **ChatComponent**: Main chat interface with message display and input
- **AppComponent**: Root component that hosts the chat

### Key Features

- **Real-time Messaging**: SSE for instant message delivery
- **Conversation Management**: Create, switch, and delete conversations
- **Model Selection**: Choose between different AI models
- **Message History**: Persistent chat history (optional)
- **Error Handling**: Graceful error handling and retry mechanisms

## Development

### Project Structure

```
src/
├── app/
│   ├── components/
│   │   └── chat/
│   │       ├── chat.component.ts
│   │       ├── chat.component.html
│   │       └── chat.component.scss
│   ├── services/
│   │   ├── mindsdb.service.ts
│   │   ├── sse-client.service.ts
│   │   └── chat.service.ts
│   ├── app.component.ts
│   ├── app.config.ts
│   └── app.routes.ts
├── environments/
│   ├── environment.ts
│   └── environment.prod.ts
└── styles.scss
```

### Available Scripts

- `ng serve` - Start development server
- `ng build` - Build for production
- `ng test` - Run unit tests
- `ng lint` - Run linting
- `ng e2e` - Run end-to-end tests

### Environment Configuration

The application supports multiple environments:

- **Development**: `src/environments/environment.ts`
- **Production**: `src/environments/environment.prod.ts`

## API Integration

### MindsDB REST API

The application uses MindsDB's REST API for:
- Model queries
- Connection testing
- Model listing
- Chat history (optional)

### Server-Sent Events

SSE endpoint for real-time features:
- Live chat responses
- Connection status updates
- Model notifications

## Customization

### Styling

- Global styles: `src/styles.scss`
- Component styles: `src/app/components/chat/chat.component.scss`
- CSS custom properties for easy theming

### Models

Add or modify AI models by updating the model selection in the chat service and ensuring the models are available in your MindsDB instance.

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Verify MindsDB is running
   - Check the `baseUrl` in environment configuration
   - Ensure CORS is properly configured

2. **SSE Not Working**
   - Verify the SSE endpoint is available
   - Check browser developer tools for connection errors
   - Ensure firewall allows the connection

3. **Model Not Found**
   - Verify the model exists in MindsDB
   - Check model name spelling
   - Ensure model is active and trained

### Debug Mode

Enable debug logging by opening browser developer tools and monitoring the console for detailed error messages.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Check the [MindsDB Documentation](https://docs.mindsdb.com/)
- Open an issue in this repository
- Join the [MindsDB Community](https://mindsdb.com/community)
