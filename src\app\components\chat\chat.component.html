<div class="chat-container">
  <!-- Sidebar -->
  <div class="sidebar" [class.hidden]="!showSidebar">
    <div class="sidebar-header">
      <h2>MindsDB Chat</h2>
      <button class="btn btn-primary" (click)="createNewConversation()">
        <i class="icon-plus"></i> New Chat
      </button>
    </div>

    <!-- Connection Status -->
    <div class="connection-status" [class.connected]="chatState.isConnected">
      <div class="status-indicator"></div>
      <span>{{ chatState.isConnected ? 'Connected' : 'Disconnected' }}</span>
    </div>

    <!-- Model Selection -->
    <div class="model-selection">
      <label for="model-select">Model:</label>
      <select 
        id="model-select" 
        [(ngModel)]="selectedModel" 
        (change)="onModelChange()"
        class="model-select">
        <option *ngFor="let model of availableModels" [value]="model">
          {{ model }}
        </option>
      </select>
    </div>

    <!-- Conversations List -->
    <div class="conversations-list">
      <h3>Conversations</h3>
      <div class="conversation-items">
        <div 
          *ngFor="let conversation of chatState.conversations" 
          class="conversation-item"
          [class.active]="chatState.currentConversation?.id === conversation.id"
          (click)="switchConversation(conversation.id)">
          <div class="conversation-content">
            <div class="conversation-title">{{ conversation.title }}</div>
            <div class="conversation-preview">{{ getConversationPreview(conversation) }}</div>
            <div class="conversation-time">{{ conversation.updatedAt | date:'short' }}</div>
          </div>
          <button 
            class="delete-btn" 
            (click)="deleteConversation(conversation.id, $event)"
            title="Delete conversation">
            <i class="icon-trash"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Chat Area -->
  <div class="chat-main" [class.full-width]="!showSidebar">
    <!-- Header -->
    <div class="chat-header">
      <button class="sidebar-toggle" (click)="toggleSidebar()">
        <i class="icon-menu"></i>
      </button>
      <div class="chat-title">
        <h1>{{ chatState.currentConversation?.title || 'MindsDB Chat' }}</h1>
        <span class="model-indicator">Using: {{ selectedModel }}</span>
      </div>
      <div class="chat-actions">
        <button
          class="btn btn-secondary"
          (click)="clearConversation()"
          [disabled]="!chatState.currentConversation?.messages?.length">
          Clear Chat
        </button>
      </div>
    </div>

    <!-- Error Display -->
    <div *ngIf="chatState.error" class="error-banner">
      <i class="icon-alert"></i>
      <span>{{ chatState.error }}</span>
      <button class="retry-btn" (click)="retryLastMessage()">Retry</button>
    </div>

    <!-- Messages Container -->
    <div class="messages-container" #messagesContainer>
      <div class="messages-list">
        <!-- Welcome Message -->
        <div *ngIf="!chatState.currentConversation?.messages?.length" class="welcome-message">
          <div class="welcome-content">
            <h2>Welcome to MindsDB Chat</h2>
            <p>Start a conversation with your AI assistant powered by MindsDB.</p>
            <div class="welcome-features">
              <div class="feature">
                <i class="icon-brain"></i>
                <span>AI-powered responses</span>
              </div>
              <div class="feature">
                <i class="icon-database"></i>
                <span>Connected to MindsDB</span>
              </div>
              <div class="feature">
                <i class="icon-realtime"></i>
                <span>Real-time communication</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Chat Messages -->
        <div 
          *ngFor="let message of chatState.currentConversation?.messages" 
          class="message"
          [class.user-message]="isUserMessage(message)"
          [class.assistant-message]="!isUserMessage(message)">
          
          <div class="message-avatar">
            <i [class]="isUserMessage(message) ? 'icon-user' : 'icon-bot'"></i>
          </div>
          
          <div class="message-content">
            <div class="message-header">
              <span class="message-role">{{ isUserMessage(message) ? 'You' : 'Assistant' }}</span>
              <span class="message-time">{{ getMessageTime(message) }}</span>
            </div>
            <div class="message-text">{{ message.content }}</div>
          </div>
        </div>

        <!-- Loading Indicator -->
        <div *ngIf="chatState.isLoading" class="message assistant-message loading">
          <div class="message-avatar">
            <i class="icon-bot"></i>
          </div>
          <div class="message-content">
            <div class="message-header">
              <span class="message-role">Assistant</span>
            </div>
            <div class="message-text">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Message Input -->
    <div class="message-input-container">
      <div class="message-input-wrapper">
        <textarea
          #messageInput
          [(ngModel)]="currentMessage"
          (keydown)="onKeyPress($event)"
          placeholder="Type your message here... (Press Enter to send, Shift+Enter for new line)"
          class="message-input"
          rows="1"
          [disabled]="chatState.isLoading || !chatState.isConnected">
        </textarea>
        <button 
          class="send-button"
          (click)="sendMessage()"
          [disabled]="!currentMessage.trim() || chatState.isLoading || !chatState.isConnected">
          <i class="icon-send"></i>
        </button>
      </div>
      <div class="input-footer">
        <span class="input-hint">
          {{ chatState.isConnected ? 'Connected to MindsDB' : 'Connecting...' }}
        </span>
      </div>
    </div>
  </div>
</div>
