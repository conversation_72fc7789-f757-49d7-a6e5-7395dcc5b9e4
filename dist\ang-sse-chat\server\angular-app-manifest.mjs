
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: [
  {
    "renderMode": 2,
    "route": "/"
  }
],
  entryPointToBrowserMapping: undefined,
  assets: {
    'index.csr.html': {size: 818, hash: '204557cd595d697995de9a0face7ddb27fde3ac7110cc340d865edf0fa08f62c', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 1007, hash: 'dce4d2a9ea683bbb00e15632ca416b9591dc2dbdf65ea614af4a2c5b700b8c2c', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'index.html': {size: 21393, hash: 'ee50cf91350c9808476c874eaf68acac7f6c40ef5a03b610c615e874052c9d25', text: () => import('./assets-chunks/index_html.mjs').then(m => m.default)},
    'styles-Z73UOTI6.css': {size: 2016, hash: 'AvKsZmhZs5E', text: () => import('./assets-chunks/styles-Z73UOTI6_css.mjs').then(m => m.default)}
  },
};
