import { Compo<PERSON>, On<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hild, ElementRef, AfterViewChecked } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { ChatService, ChatState } from '../../services/chat.service';
import { ChatMessage } from '../../services/mindsdb.service';

@Component({
  selector: 'app-chat',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss']
})
export class ChatComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  chatState: ChatState = {
    currentConversation: null,
    conversations: [],
    isLoading: false,
    error: null,
    isConnected: false
  };

  currentMessage = '';
  availableModels: string[] = [];
  selectedModel = 'gpt-4';
  showSidebar = true;

  private destroy$ = new Subject<void>();
  private shouldScrollToBottom = false;

  constructor(private chatService: ChatService) {}

  ngOnInit(): void {
    // Subscribe to chat state changes
    this.chatService.state$
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => {
        this.chatState = state;
        this.shouldScrollToBottom = true;
      });

    // Subscribe to new messages
    this.chatService.newMessage$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.shouldScrollToBottom = true;
      });

    // Load available models
    this.loadAvailableModels();

    // Create initial conversation if none exists
    if (!this.chatState.currentConversation) {
      this.createNewConversation();
    }
  }

  ngAfterViewChecked(): void {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Send a message
   */
  sendMessage(): void {
    if (!this.currentMessage.trim() || this.chatState.isLoading) {
      return;
    }

    const message = this.currentMessage.trim();
    this.currentMessage = '';

    this.chatService.sendMessage(message).subscribe({
      next: () => {
        // Message sent successfully
        this.focusInput();
      },
      error: (error) => {
        console.error('Failed to send message:', error);
        // Optionally restore the message
        this.currentMessage = message;
      }
    });
  }

  /**
   * Handle Enter key press
   */
  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  /**
   * Create a new conversation
   */
  createNewConversation(): void {
    this.chatService.createConversation();
    this.focusInput();
  }

  /**
   * Switch to a different conversation
   */
  switchConversation(conversationId: string): void {
    this.chatService.switchConversation(conversationId);
    this.focusInput();
  }

  /**
   * Delete a conversation
   */
  deleteConversation(conversationId: string, event: Event): void {
    event.stopPropagation();
    if (confirm('Are you sure you want to delete this conversation?')) {
      this.chatService.deleteConversation(conversationId);
    }
  }

  /**
   * Clear current conversation
   */
  clearConversation(): void {
    if (confirm('Are you sure you want to clear this conversation?')) {
      this.chatService.clearCurrentConversation();
    }
  }

  /**
   * Change the selected model
   */
  onModelChange(): void {
    this.chatService.setModel(this.selectedModel);
  }

  /**
   * Toggle sidebar visibility
   */
  toggleSidebar(): void {
    this.showSidebar = !this.showSidebar;
  }

  /**
   * Get message timestamp for display
   */
  getMessageTime(message: ChatMessage): string {
    return message.timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }

  /**
   * Get conversation preview text
   */
  getConversationPreview(conversation: any): string {
    if (conversation.messages.length === 0) {
      return 'New conversation';
    }
    const lastMessage = conversation.messages[conversation.messages.length - 1];
    return lastMessage.content.substring(0, 50) + (lastMessage.content.length > 50 ? '...' : '');
  }

  /**
   * Check if message is from user
   */
  isUserMessage(message: ChatMessage): boolean {
    return message.role === 'user';
  }

  /**
   * Retry sending a message
   */
  retryLastMessage(): void {
    const conversation = this.chatState.currentConversation;
    if (conversation && conversation.messages.length > 0) {
      const lastUserMessage = [...conversation.messages]
        .reverse()
        .find(msg => msg.role === 'user');
      
      if (lastUserMessage) {
        this.currentMessage = lastUserMessage.content;
        this.focusInput();
      }
    }
  }

  private loadAvailableModels(): void {
    this.chatService.getAvailableModels().subscribe({
      next: (models) => {
        this.availableModels = models;
        if (models.length > 0 && !models.includes(this.selectedModel)) {
          this.selectedModel = models[0];
          this.onModelChange();
        }
      },
      error: (error) => {
        console.error('Failed to load models:', error);
        // Use default models if loading fails
        this.availableModels = ['gpt-4', 'gpt-3.5-turbo', 'claude-3'];
      }
    });
  }

  private scrollToBottom(): void {
    if (this.messagesContainer) {
      const element = this.messagesContainer.nativeElement;
      element.scrollTop = element.scrollHeight;
    }
  }

  private focusInput(): void {
    setTimeout(() => {
      if (this.messageInput) {
        this.messageInput.nativeElement.focus();
      }
    }, 100);
  }
}
