import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  BrowserDomAdapter,
  BrowserGetTestability,
  BrowserModule,
  By,
  DomEventsPlugin,
  DomRendererFactory2,
  Dom<PERSON><PERSON><PERSON>zer,
  Dom<PERSON><PERSON><PERSON>zer<PERSON>mpl,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerGesturesPlugin,
  HammerModule,
  HydrationFeatureKind,
  KeyEventsPlugin,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  SharedStylesHost,
  Title,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache
} from "./chunk-EQ4IAMI2.js";
import "./chunk-SVWSKDNZ.js";
import {
  getDOM
} from "./chunk-7VVESGAN.js";
import "./chunk-62SVJMVC.js";
import "./chunk-LEZCLC5X.js";
import "./chunk-ZUJ64LXG.js";
import "./chunk-XCIYP5SE.js";
import "./chunk-OYTRG5F6.js";
import "./chunk-YHCV7DAQ.js";
export {
  BrowserModule,
  By,
  DomSanitizer,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  Title,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache,
  BrowserDomAdapter as ɵBrowserDomAdapter,
  BrowserGetTestability as ɵBrowserGetTestability,
  DomEventsPlugin as ɵDomEventsPlugin,
  DomRendererFactory2 as ɵDomRendererFactory2,
  DomSanitizerImpl as ɵDomSanitizerImpl,
  HammerGesturesPlugin as ɵHammerGesturesPlugin,
  KeyEventsPlugin as ɵKeyEventsPlugin,
  SharedStylesHost as ɵSharedStylesHost,
  getDOM as ɵgetDOM
};
