.chat-container {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

// Sidebar Styles
.sidebar {
  width: 300px;
  background: #2c3e50;
  color: white;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;

  &.hidden {
    transform: translateX(-100%);
    width: 0;
  }

  .sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #34495e;

    h2 {
      margin: 0 0 15px 0;
      font-size: 1.5rem;
      font-weight: 600;
    }

    .btn {
      width: 100%;
      padding: 10px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;

      &.btn-primary {
        background: #3498db;
        color: white;

        &:hover {
          background: #2980b9;
        }
      }
    }
  }

  .connection-status {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 1px solid #34495e;

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #e74c3c;
    }

    &.connected .status-indicator {
      background: #27ae60;
    }
  }

  .model-selection {
    padding: 15px 20px;
    border-bottom: 1px solid #34495e;

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .model-select {
      width: 100%;
      padding: 8px;
      border: 1px solid #34495e;
      border-radius: 4px;
      background: #34495e;
      color: white;
    }
  }

  .conversations-list {
    flex: 1;
    overflow-y: auto;

    h3 {
      padding: 15px 20px 10px;
      margin: 0;
      font-size: 1rem;
      font-weight: 600;
      border-bottom: 1px solid #34495e;
    }

    .conversation-items {
      padding: 10px 0;
    }

    .conversation-item {
      padding: 12px 20px;
      cursor: pointer;
      border-bottom: 1px solid #34495e;
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: background 0.2s;

      &:hover {
        background: #34495e;
      }

      &.active {
        background: #3498db;
      }

      .conversation-content {
        flex: 1;
        min-width: 0;

        .conversation-title {
          font-weight: 500;
          margin-bottom: 4px;
        }

        .conversation-preview {
          font-size: 0.85rem;
          opacity: 0.7;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .conversation-time {
          font-size: 0.75rem;
          opacity: 0.5;
          margin-top: 4px;
        }
      }

      .delete-btn {
        background: none;
        border: none;
        color: #e74c3c;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        opacity: 0;
        transition: opacity 0.2s;

        &:hover {
          background: #e74c3c;
          color: white;
        }
      }

      &:hover .delete-btn {
        opacity: 1;
      }
    }
  }
}

// Main Chat Area
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;

  &.full-width {
    width: 100%;
  }

  .chat-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    align-items: center;
    gap: 15px;
    background: white;
    z-index: 10;

    .sidebar-toggle {
      background: none;
      border: none;
      font-size: 1.2rem;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;

      &:hover {
        background: #f8f9fa;
      }
    }

    .chat-title {
      flex: 1;

      h1 {
        margin: 0;
        font-size: 1.3rem;
        font-weight: 600;
      }

      .model-indicator {
        font-size: 0.85rem;
        color: #666;
      }
    }

    .chat-actions {
      .btn {
        padding: 8px 16px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
        cursor: pointer;

        &:hover:not(:disabled) {
          background: #f8f9fa;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  .error-banner {
    background: #fee;
    color: #c33;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 1px solid #fcc;

    .retry-btn {
      background: #c33;
      color: white;
      border: none;
      padding: 4px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.85rem;

      &:hover {
        background: #a22;
      }
    }
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;

    .welcome-message {
      text-align: center;
      padding: 60px 20px;

      .welcome-content {
        max-width: 500px;
        margin: 0 auto;

        h2 {
          margin-bottom: 15px;
          color: #2c3e50;
        }

        p {
          color: #666;
          margin-bottom: 30px;
        }

        .welcome-features {
          display: flex;
          justify-content: center;
          gap: 30px;
          flex-wrap: wrap;

          .feature {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #3498db;
            font-weight: 500;
          }
        }
      }
    }

    .message {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;

      &.user-message {
        flex-direction: row-reverse;

        .message-content {
          background: #3498db;
          color: white;
        }
      }

      &.assistant-message .message-content {
        background: #f8f9fa;
        border: 1px solid #e1e8ed;
      }

      .message-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .message-content {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 12px;

        .message-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;
          font-size: 0.85rem;
          opacity: 0.8;
        }

        .message-text {
          line-height: 1.5;
          white-space: pre-wrap;
        }
      }

      &.loading .message-text {
        padding: 8px 0;
      }
    }
  }

  .message-input-container {
    border-top: 1px solid #e1e8ed;
    padding: 20px;
    background: white;

    .message-input-wrapper {
      display: flex;
      gap: 12px;
      align-items: flex-end;

      .message-input {
        flex: 1;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 12px;
        resize: none;
        font-family: inherit;
        font-size: 1rem;
        line-height: 1.4;
        max-height: 120px;

        &:focus {
          outline: none;
          border-color: #3498db;
        }

        &:disabled {
          background: #f8f9fa;
          opacity: 0.6;
        }
      }

      .send-button {
        background: #3498db;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover:not(:disabled) {
          background: #2980b9;
        }

        &:disabled {
          background: #bdc3c7;
          cursor: not-allowed;
        }
      }
    }

    .input-footer {
      margin-top: 8px;
      text-align: center;

      .input-hint {
        font-size: 0.8rem;
        color: #666;
      }
    }
  }
}

// Typing Indicator
.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;

  span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #bbb;
    animation: typing 1.4s infinite ease-in-out;

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// Icons (using text for now, can be replaced with icon font)
.icon-plus::before { content: '+'; }
.icon-menu::before { content: '☰'; }
.icon-user::before { content: '👤'; }
.icon-bot::before { content: '🤖'; }
.icon-send::before { content: '➤'; }
.icon-trash::before { content: '🗑'; }
.icon-alert::before { content: '⚠'; }
.icon-brain::before { content: '🧠'; }
.icon-database::before { content: '💾'; }
.icon-realtime::before { content: '⚡'; }

// Responsive Design
@media (max-width: 768px) {
  .sidebar {
    position: absolute;
    z-index: 100;
    height: 100%;

    &.hidden {
      transform: translateX(-100%);
    }
  }

  .chat-main {
    width: 100%;
  }

  .message .message-content {
    max-width: 85%;
  }
}
