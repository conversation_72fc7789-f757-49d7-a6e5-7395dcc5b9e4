var OD=Object.defineProperty,PD=Object.defineProperties;var kD=Object.getOwnPropertyDescriptors;var li=Object.getOwnPropertySymbols;var vf=Object.prototype.hasOwnProperty,yf=Object.prototype.propertyIsEnumerable;var mf=(e,t,n)=>t in e?OD(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,m=(e,t)=>{for(var n in t||={})vf.call(t,n)&&mf(e,n,t[n]);if(li)for(var n of li(t))yf.call(t,n)&&mf(e,n,t[n]);return e},O=(e,t)=>PD(e,kD(t));var Df=(e,t)=>{var n={};for(var r in e)vf.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&li)for(var r of li(e))t.indexOf(r)<0&&yf.call(e,r)&&(n[r]=e[r]);return n};var Ut=(e,t,n)=>new Promise((r,o)=>{var i=c=>{try{a(n.next(c))}catch(l){o(l)}},s=c=>{try{a(n.throw(c))}catch(l){o(l)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,s);a((n=n.apply(e,t)).next())});function hc(e,t){return Object.is(e,t)}var ie=null,ui=!1,pc=1,$e=Symbol("SIGNAL");function k(e){let t=ie;return ie=e,t}function gc(){return ie}var qr={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Wr(e){if(ui)throw new Error("");if(ie===null)return;ie.consumerOnSignalRead(e);let t=ie.nextProducerIndex++;if(pi(ie),t<ie.producerNode.length&&ie.producerNode[t]!==e&&Gr(ie)){let n=ie.producerNode[t];hi(n,ie.producerIndexOfThis[t])}ie.producerNode[t]!==e&&(ie.producerNode[t]=e,ie.producerIndexOfThis[t]=Gr(ie)?Ef(e,ie,t):0),ie.producerLastReadVersion[t]=e.version}function Cf(){pc++}function mc(e){if(!(Gr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===pc)){if(!e.producerMustRecompute(e)&&!Cc(e)){fc(e);return}e.producerRecomputeValue(e),fc(e)}}function vc(e){if(e.liveConsumerNode===void 0)return;let t=ui;ui=!0;try{for(let n of e.liveConsumerNode)n.dirty||FD(n)}finally{ui=t}}function yc(){return ie?.consumerAllowSignalWrites!==!1}function FD(e){e.dirty=!0,vc(e),e.consumerMarkedDirty?.(e)}function fc(e){e.dirty=!1,e.lastCleanEpoch=pc}function fi(e){return e&&(e.nextProducerIndex=0),k(e)}function Dc(e,t){if(k(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Gr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)hi(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Cc(e){pi(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(mc(n),r!==n.version))return!0}return!1}function Ec(e){if(pi(e),Gr(e))for(let t=0;t<e.producerNode.length;t++)hi(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Ef(e,t,n){if(wf(e),e.liveConsumerNode.length===0&&_f(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Ef(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function hi(e,t){if(wf(e),e.liveConsumerNode.length===1&&_f(e))for(let r=0;r<e.producerNode.length;r++)hi(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];pi(o),o.producerIndexOfThis[r]=t}}function Gr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function pi(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function wf(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function _f(e){return e.producerNode!==void 0}function wc(e,t){let n=Object.create(LD);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(mc(n),Wr(n),n.value===di)throw n.error;return n.value};return r[$e]=n,r}var uc=Symbol("UNSET"),dc=Symbol("COMPUTING"),di=Symbol("ERRORED"),LD=O(m({},qr),{value:uc,dirty:!0,error:null,equal:hc,kind:"computed",producerMustRecompute(e){return e.value===uc||e.value===dc},producerRecomputeValue(e){if(e.value===dc)throw new Error("Detected cycle in computations.");let t=e.value;e.value=dc;let n=fi(e),r,o=!1;try{r=e.computation(),k(null),o=t!==uc&&t!==di&&r!==di&&e.equal(t,r)}catch(i){r=di,e.error=i}finally{Dc(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function VD(){throw new Error}var bf=VD;function If(e){bf(e)}function _c(e){bf=e}var jD=null;function bc(e,t){let n=Object.create(gi);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(Wr(n),n.value);return r[$e]=n,r}function Zr(e,t){yc()||If(e),e.equal(e.value,t)||(e.value=t,BD(e))}function Ic(e,t){yc()||If(e),Zr(e,t(e.value))}var gi=O(m({},qr),{equal:hc,value:void 0,kind:"signal"});function BD(e){e.version++,Cf(),vc(e),jD?.()}function Mc(e){let t=k(null);try{return e()}finally{k(t)}}var Sc;function Yr(){return Sc}function bt(e){let t=Sc;return Sc=e,t}var mi=Symbol("NotFound");function T(e){return typeof e=="function"}function Gn(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var vi=Gn(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Qr(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var ne=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(T(r))try{r()}catch(i){t=i instanceof vi?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Mf(i)}catch(s){t=t??[],s instanceof vi?t=[...t,...s.errors]:t.push(s)}}if(t)throw new vi(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Mf(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Qr(n,t)}remove(t){let{_finalizers:n}=this;n&&Qr(n,t),t instanceof e&&t._removeParent(this)}};ne.EMPTY=(()=>{let e=new ne;return e.closed=!0,e})();var Tc=ne.EMPTY;function yi(e){return e instanceof ne||e&&"closed"in e&&T(e.remove)&&T(e.add)&&T(e.unsubscribe)}function Mf(e){T(e)?e():e.unsubscribe()}var Xe={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var qn={setTimeout(e,t,...n){let{delegate:r}=qn;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=qn;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Di(e){qn.setTimeout(()=>{let{onUnhandledError:t}=Xe;if(t)t(e);else throw e})}function Kr(){}var Sf=Ac("C",void 0,void 0);function Tf(e){return Ac("E",void 0,e)}function Af(e){return Ac("N",e,void 0)}function Ac(e,t,n){return{kind:e,value:t,error:n}}var un=null;function Wn(e){if(Xe.useDeprecatedSynchronousErrorHandling){let t=!un;if(t&&(un={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=un;if(un=null,n)throw r}}else e()}function Nf(e){Xe.useDeprecatedSynchronousErrorHandling&&un&&(un.errorThrown=!0,un.error=e)}var dn=class extends ne{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,yi(t)&&t.add(this)):this.destination=qD}static create(t,n,r){return new Zn(t,n,r)}next(t){this.isStopped?xc(Af(t),this):this._next(t)}error(t){this.isStopped?xc(Tf(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?xc(Sf,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},zD=Function.prototype.bind;function Nc(e,t){return zD.call(e,t)}var Rc=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Ci(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Ci(r)}else Ci(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Ci(n)}}},Zn=class extends dn{constructor(t,n,r){super();let o;if(T(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Xe.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Nc(t.next,i),error:t.error&&Nc(t.error,i),complete:t.complete&&Nc(t.complete,i)}):o=t}this.destination=new Rc(o)}};function Ci(e){Xe.useDeprecatedSynchronousErrorHandling?Nf(e):Di(e)}function GD(e){throw e}function xc(e,t){let{onStoppedNotification:n}=Xe;n&&qn.setTimeout(()=>n(e,t))}var qD={closed:!0,next:Kr,error:GD,complete:Kr};var Yn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Re(e){return e}function Oc(...e){return Pc(e)}function Pc(e){return e.length===0?Re:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var V=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=ZD(n)?n:new Zn(n,r,o);return Wn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=xf(r),new r((o,i)=>{let s=new Zn({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Yn](){return this}pipe(...n){return Pc(n)(this)}toPromise(n){return n=xf(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function xf(e){var t;return(t=e??Xe.Promise)!==null&&t!==void 0?t:Promise}function WD(e){return e&&T(e.next)&&T(e.error)&&T(e.complete)}function ZD(e){return e&&e instanceof dn||WD(e)&&yi(e)}function kc(e){return T(e?.lift)}function j(e){return t=>{if(kc(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function F(e,t,n,r,o){return new Fc(e,t,n,r,o)}var Fc=class extends dn{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Qn(){return j((e,t)=>{let n=null;e._refCount++;let r=F(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Kn=class extends V{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,kc(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new ne;let n=this.getSubject();t.add(this.source.subscribe(F(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=ne.EMPTY)}return t}refCount(){return Qn()(this)}};var Rf=Gn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var W=(()=>{class e extends V{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Ei(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Rf}next(n){Wn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Wn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Wn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Tc:(this.currentObservers=null,i.push(n),new ne(()=>{this.currentObservers=null,Qr(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new V;return n.source=this,n}}return e.create=(t,n)=>new Ei(t,n),e})(),Ei=class extends W{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Tc}};var X=class extends W{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Ee=new V(e=>e.complete());function Of(e){return e&&T(e.schedule)}function Pf(e){return e[e.length-1]}function wi(e){return T(Pf(e))?e.pop():void 0}function $t(e){return Of(Pf(e))?e.pop():void 0}function Ff(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(f){s(f)}}function c(u){try{l(r.throw(u))}catch(f){s(f)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function kf(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function fn(e){return this instanceof fn?(this.v=e,this):new fn(e)}function Lf(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(d){return function(p){return Promise.resolve(p).then(d,f)}}function a(d,p){r[d]&&(o[d]=function(v){return new Promise(function(E,L){i.push([d,v,E,L])>1||c(d,v)})},p&&(o[d]=p(o[d])))}function c(d,p){try{l(r[d](p))}catch(v){h(i[0][3],v)}}function l(d){d.value instanceof fn?Promise.resolve(d.value.v).then(u,f):h(i[0][2],d)}function u(d){c("next",d)}function f(d){c("throw",d)}function h(d,p){d(p),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Vf(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof kf=="function"?kf(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var _i=e=>e&&typeof e.length=="number"&&typeof e!="function";function bi(e){return T(e?.then)}function Ii(e){return T(e[Yn])}function Mi(e){return Symbol.asyncIterator&&T(e?.[Symbol.asyncIterator])}function Si(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function YD(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Ti=YD();function Ai(e){return T(e?.[Ti])}function Ni(e){return Lf(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield fn(n.read());if(o)return yield fn(void 0);yield yield fn(r)}}finally{n.releaseLock()}})}function xi(e){return T(e?.getReader)}function re(e){if(e instanceof V)return e;if(e!=null){if(Ii(e))return QD(e);if(_i(e))return KD(e);if(bi(e))return JD(e);if(Mi(e))return jf(e);if(Ai(e))return XD(e);if(xi(e))return eC(e)}throw Si(e)}function QD(e){return new V(t=>{let n=e[Yn]();if(T(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function KD(e){return new V(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function JD(e){return new V(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Di)})}function XD(e){return new V(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function jf(e){return new V(t=>{tC(e,t).catch(n=>t.error(n))})}function eC(e){return jf(Ni(e))}function tC(e,t){var n,r,o,i;return Ff(this,void 0,void 0,function*(){try{for(n=Vf(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function we(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Ri(e,t=0){return j((n,r)=>{n.subscribe(F(r,o=>we(r,e,()=>r.next(o),t),()=>we(r,e,()=>r.complete(),t),o=>we(r,e,()=>r.error(o),t)))})}function Oi(e,t=0){return j((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Bf(e,t){return re(e).pipe(Oi(t),Ri(t))}function Uf(e,t){return re(e).pipe(Oi(t),Ri(t))}function $f(e,t){return new V(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Hf(e,t){return new V(n=>{let r;return we(n,t,()=>{r=e[Ti](),we(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>T(r?.return)&&r.return()})}function Pi(e,t){if(!e)throw new Error("Iterable cannot be null");return new V(n=>{we(n,t,()=>{let r=e[Symbol.asyncIterator]();we(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function zf(e,t){return Pi(Ni(e),t)}function Gf(e,t){if(e!=null){if(Ii(e))return Bf(e,t);if(_i(e))return $f(e,t);if(bi(e))return Uf(e,t);if(Mi(e))return Pi(e,t);if(Ai(e))return Hf(e,t);if(xi(e))return zf(e,t)}throw Si(e)}function K(e,t){return t?Gf(e,t):re(e)}function b(...e){let t=$t(e);return K(e,t)}function It(e,t){let n=T(e)?e:()=>e,r=o=>o.error(n());return new V(t?o=>t.schedule(r,0,o):r)}function Lc(e){return!!e&&(e instanceof V||T(e.lift)&&T(e.subscribe))}var Mt=Gn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function I(e,t){return j((n,r)=>{let o=0;n.subscribe(F(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:nC}=Array;function rC(e,t){return nC(t)?e(...t):e(t)}function ki(e){return I(t=>rC(e,t))}var{isArray:oC}=Array,{getPrototypeOf:iC,prototype:sC,keys:aC}=Object;function Fi(e){if(e.length===1){let t=e[0];if(oC(t))return{args:t,keys:null};if(cC(t)){let n=aC(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function cC(e){return e&&typeof e=="object"&&iC(e)===sC}function Li(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Vi(...e){let t=$t(e),n=wi(e),{args:r,keys:o}=Fi(e);if(r.length===0)return K([],t);let i=new V(lC(r,t,o?s=>Li(o,s):Re));return n?i.pipe(ki(n)):i}function lC(e,t,n=Re){return r=>{qf(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)qf(t,()=>{let l=K(e[c],t),u=!1;l.subscribe(F(r,f=>{i[c]=f,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function qf(e,t,n){e?we(n,e,t):t()}function Wf(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,f=!1,h=()=>{f&&!c.length&&!l&&t.complete()},d=v=>l<r?p(v):c.push(v),p=v=>{i&&t.next(v),l++;let E=!1;re(n(v,u++)).subscribe(F(t,L=>{o?.(L),i?d(L):t.next(L)},()=>{E=!0},void 0,()=>{if(E)try{for(l--;c.length&&l<r;){let L=c.shift();s?we(t,s,()=>p(L)):p(L)}h()}catch(L){t.error(L)}}))};return e.subscribe(F(t,d,()=>{f=!0,h()})),()=>{a?.()}}function se(e,t,n=1/0){return T(t)?se((r,o)=>I((i,s)=>t(r,i,o,s))(re(e(r,o))),n):(typeof t=="number"&&(n=t),j((r,o)=>Wf(r,o,e,n)))}function Zf(e=1/0){return se(Re,e)}function Yf(){return Zf(1)}function Jn(...e){return Yf()(K(e,$t(e)))}function ji(e){return new V(t=>{re(e()).subscribe(t)})}function Vc(...e){let t=wi(e),{args:n,keys:r}=Fi(e),o=new V(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let f=!1;re(n[u]).subscribe(F(i,h=>{f||(f=!0,l--),a[u]=h},()=>c--,void 0,()=>{(!c||!f)&&(l||i.next(r?Li(r,a):a),i.complete())}))}});return t?o.pipe(ki(t)):o}function Oe(e,t){return j((n,r)=>{let o=0;n.subscribe(F(r,i=>e.call(t,i,o++)&&r.next(i)))})}function _e(e){return j((t,n)=>{let r=null,o=!1,i;r=t.subscribe(F(n,void 0,void 0,s=>{i=re(e(s,_e(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Qf(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(F(s,u=>{let f=l++;c=a?e(c,u,f):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Ht(e,t){return T(t)?se(e,t,1):se(e,1)}function zt(e){return j((t,n)=>{let r=!1;t.subscribe(F(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function St(e){return e<=0?()=>Ee:j((t,n)=>{let r=0;t.subscribe(F(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Bi(e=uC){return j((t,n)=>{let r=!1;t.subscribe(F(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function uC(){return new Mt}function Gt(e){return j((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Tt(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Oe((o,i)=>e(o,i,r)):Re,St(1),n?zt(t):Bi(()=>new Mt))}function Xn(e){return e<=0?()=>Ee:j((t,n)=>{let r=[];t.subscribe(F(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function jc(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Oe((o,i)=>e(o,i,r)):Re,Xn(1),n?zt(t):Bi(()=>new Mt))}function Bc(e,t){return j(Qf(e,t,arguments.length>=2,!0))}function Uc(...e){let t=$t(e);return j((n,r)=>{(t?Jn(e,n,t):Jn(e,n)).subscribe(r)})}function Pe(e,t){return j((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(F(r,c=>{o?.unsubscribe();let l=0,u=i++;re(e(c,u)).subscribe(o=F(r,f=>r.next(t?t(c,f,u,l++):f),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function er(e){return j((t,n)=>{re(e).subscribe(F(n,()=>n.complete(),Kr)),!n.closed&&t.subscribe(n)})}function ae(e,t,n){let r=T(e)||t||n?{next:e,error:t,complete:n}:e;return r?j((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(F(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):Re}var qc={JSACTION:"jsaction"},Wc={JSACTION:"__jsaction",OWNER:"__owner"},eh={};function dC(e){return e[Wc.JSACTION]}function Kf(e,t){e[Wc.JSACTION]=t}function fC(e){return eh[e]}function hC(e,t){eh[e]=t}var _={CLICK:"click",CLICKMOD:"clickmod",DBLCLICK:"dblclick",FOCUS:"focus",FOCUSIN:"focusin",BLUR:"blur",FOCUSOUT:"focusout",SUBMIT:"submit",KEYDOWN:"keydown",KEYPRESS:"keypress",KEYUP:"keyup",MOUSEOVER:"mouseover",MOUSEOUT:"mouseout",MOUSEENTER:"mouseenter",MOUSELEAVE:"mouseleave",POINTEROVER:"pointerover",POINTEROUT:"pointerout",POINTERENTER:"pointerenter",POINTERLEAVE:"pointerleave",ERROR:"error",LOAD:"load",TOUCHSTART:"touchstart",TOUCHEND:"touchend",TOUCHMOVE:"touchmove",TOGGLE:"toggle"},pC=[_.MOUSEENTER,_.MOUSELEAVE,"pointerenter","pointerleave"],dF=[_.CLICK,_.DBLCLICK,_.FOCUSIN,_.FOCUSOUT,_.KEYDOWN,_.KEYUP,_.KEYPRESS,_.MOUSEOVER,_.MOUSEOUT,_.SUBMIT,_.TOUCHSTART,_.TOUCHEND,_.TOUCHMOVE,"touchcancel","auxclick","change","compositionstart","compositionupdate","compositionend","beforeinput","input","select","copy","cut","paste","mousedown","mouseup","wheel","contextmenu","dragover","dragenter","dragleave","drop","dragstart","dragend","pointerdown","pointermove","pointerup","pointercancel","pointerover","pointerout","gotpointercapture","lostpointercapture","ended","loadedmetadata","pagehide","pageshow","visibilitychange","beforematch"],gC=[_.FOCUS,_.BLUR,_.ERROR,_.LOAD,_.TOGGLE],Zc=e=>gC.indexOf(e)>=0;function mC(e){return e===_.MOUSEENTER?_.MOUSEOVER:e===_.MOUSELEAVE?_.MOUSEOUT:e===_.POINTERENTER?_.POINTEROVER:e===_.POINTERLEAVE?_.POINTEROUT:e}function vC(e,t,n,r){let o=!1;Zc(t)&&(o=!0);let i=typeof r=="boolean"?{capture:o,passive:r}:o;return e.addEventListener(t,n,i),{eventType:t,handler:n,capture:o,passive:r}}function yC(e,t){if(e.removeEventListener){let n=typeof t.passive=="boolean"?{capture:t.capture}:t.capture;e.removeEventListener(t.eventType,t.handler,n)}else e.detachEvent&&e.detachEvent(`on${t.eventType}`,t.handler)}function DC(e){e.preventDefault?e.preventDefault():e.returnValue=!1}var Jf=typeof navigator<"u"&&/Macintosh/.test(navigator.userAgent);function CC(e){return e.which===2||e.which==null&&e.button===4}function EC(e){return Jf&&e.metaKey||!Jf&&e.ctrlKey||CC(e)||e.shiftKey}function wC(e,t,n){let r=e.relatedTarget;return(e.type===_.MOUSEOVER&&t===_.MOUSEENTER||e.type===_.MOUSEOUT&&t===_.MOUSELEAVE||e.type===_.POINTEROVER&&t===_.POINTERENTER||e.type===_.POINTEROUT&&t===_.POINTERLEAVE)&&(!r||r!==n&&!n.contains(r))}function _C(e,t){let n={};for(let r in e){if(r==="srcElement"||r==="target")continue;let o=r,i=e[o];typeof i!="function"&&(n[o]=i)}return e.type===_.MOUSEOVER?n.type=_.MOUSEENTER:e.type===_.MOUSEOUT?n.type=_.MOUSELEAVE:e.type===_.POINTEROVER?n.type=_.POINTERENTER:n.type=_.POINTERLEAVE,n.target=n.srcElement=t,n.bubbles=!1,n._originalEvent=e,n}var bC=typeof navigator<"u"&&/iPhone|iPad|iPod/.test(navigator.userAgent),zi=class{element;handlerInfos=[];constructor(t){this.element=t}addEventListener(t,n,r){bC&&(this.element.style.cursor="pointer"),this.handlerInfos.push(vC(this.element,t,n(this.element),r))}cleanUp(){for(let t=0;t<this.handlerInfos.length;t++)yC(this.element,this.handlerInfos[t]);this.handlerInfos=[]}},IC={EVENT_ACTION_SEPARATOR:":"};function qt(e){return e.eventType}function Yc(e,t){e.eventType=t}function $i(e){return e.event}function th(e,t){e.event=t}function nh(e){return e.targetElement}function rh(e,t){e.targetElement=t}function oh(e){return e.eic}function MC(e,t){e.eic=t}function SC(e){return e.timeStamp}function TC(e,t){e.timeStamp=t}function Hi(e){return e.eia}function ih(e,t,n){e.eia=[t,n]}function $c(e){e.eia=void 0}function Ui(e){return e[1]}function AC(e){return e.eirp}function sh(e,t){e.eirp=t}function ah(e){return e.eir}function ch(e,t){e.eir=t}function lh(e){return{eventType:e.eventType,event:e.event,targetElement:e.targetElement,eic:e.eic,eia:e.eia,timeStamp:e.timeStamp,eirp:e.eirp,eiack:e.eiack,eir:e.eir}}function NC(e,t,n,r,o,i,s,a){return{eventType:e,event:t,targetElement:n,eic:r,timeStamp:o,eia:i,eirp:s,eiack:a}}var Hc=class e{eventInfo;constructor(t){this.eventInfo=t}getEventType(){return qt(this.eventInfo)}setEventType(t){Yc(this.eventInfo,t)}getEvent(){return $i(this.eventInfo)}setEvent(t){th(this.eventInfo,t)}getTargetElement(){return nh(this.eventInfo)}setTargetElement(t){rh(this.eventInfo,t)}getContainer(){return oh(this.eventInfo)}setContainer(t){MC(this.eventInfo,t)}getTimestamp(){return SC(this.eventInfo)}setTimestamp(t){TC(this.eventInfo,t)}getAction(){let t=Hi(this.eventInfo);if(t)return{name:t[0],element:t[1]}}setAction(t){if(!t){$c(this.eventInfo);return}ih(this.eventInfo,t.name,t.element)}getIsReplay(){return AC(this.eventInfo)}setIsReplay(t){sh(this.eventInfo,t)}getResolved(){return ah(this.eventInfo)}setResolved(t){ch(this.eventInfo,t)}clone(){return new e(lh(this.eventInfo))}},xC={},RC=/\s*;\s*/,OC=_.CLICK,zc=class{a11yClickSupport=!1;clickModSupport=!0;syntheticMouseEventSupport;updateEventInfoForA11yClick=void 0;preventDefaultForA11yClick=void 0;populateClickOnlyAction=void 0;constructor({syntheticMouseEventSupport:t=!1,clickModSupport:n=!0}={}){this.syntheticMouseEventSupport=t,this.clickModSupport=n}resolveEventType(t){this.clickModSupport&&qt(t)===_.CLICK&&EC($i(t))?Yc(t,_.CLICKMOD):this.a11yClickSupport&&this.updateEventInfoForA11yClick(t)}resolveAction(t){ah(t)||(this.populateAction(t,nh(t)),ch(t,!0))}resolveParentAction(t){let n=Hi(t),r=n&&Ui(n);$c(t);let o=r&&this.getParentNode(r);o&&this.populateAction(t,o)}populateAction(t,n){let r=n;for(;r&&r!==oh(t)&&(r.nodeType===Node.ELEMENT_NODE&&this.populateActionOnElement(r,t),!Hi(t));)r=this.getParentNode(r);let o=Hi(t);if(o&&(this.a11yClickSupport&&this.preventDefaultForA11yClick(t),this.syntheticMouseEventSupport&&(qt(t)===_.MOUSEENTER||qt(t)===_.MOUSELEAVE||qt(t)===_.POINTERENTER||qt(t)===_.POINTERLEAVE)))if(wC($i(t),qt(t),Ui(o))){let i=_C($i(t),Ui(o));th(t,i),rh(t,Ui(o))}else $c(t)}getParentNode(t){let n=t[Wc.OWNER];if(n)return n;let r=t.parentNode;return r?.nodeName==="#document-fragment"?r?.host??null:r}populateActionOnElement(t,n){let r=this.parseActions(t),o=r[qt(n)];o!==void 0&&ih(n,o,t),this.a11yClickSupport&&this.populateClickOnlyAction(t,n,r)}parseActions(t){let n=dC(t);if(!n){let r=t.getAttribute(qc.JSACTION);if(!r)n=xC,Kf(t,n);else{if(n=fC(r),!n){n={};let o=r.split(RC);for(let i=0;i<o.length;i++){let s=o[i];if(!s)continue;let a=s.indexOf(IC.EVENT_ACTION_SEPARATOR),c=a!==-1,l=c?s.substr(0,a).trim():OC,u=c?s.substr(a+1).trim():s;n[l]=u}hC(r,n)}Kf(t,n)}}return n}addA11yClickSupport(t,n,r){this.a11yClickSupport=!0,this.updateEventInfoForA11yClick=t,this.preventDefaultForA11yClick=n,this.populateClickOnlyAction=r}},uh=function(e){return e[e.I_AM_THE_JSACTION_FRAMEWORK=0]="I_AM_THE_JSACTION_FRAMEWORK",e}(uh||{}),Gc=class{dispatchDelegate;actionResolver;eventReplayer;eventReplayScheduled=!1;replayEventInfoWrappers=[];constructor(t,{actionResolver:n,eventReplayer:r}={}){this.dispatchDelegate=t,this.actionResolver=n,this.eventReplayer=r}dispatch(t){let n=new Hc(t);this.actionResolver?.resolveEventType(t),this.actionResolver?.resolveAction(t);let r=n.getAction();if(r&&PC(r.element,n)&&DC(n.getEvent()),this.eventReplayer&&n.getIsReplay()){this.scheduleEventInfoWrapperReplay(n);return}this.dispatchDelegate(n)}scheduleEventInfoWrapperReplay(t){this.replayEventInfoWrappers.push(t),!this.eventReplayScheduled&&(this.eventReplayScheduled=!0,Promise.resolve().then(()=>{this.eventReplayScheduled=!1,this.eventReplayer(this.replayEventInfoWrappers)}))}};function PC(e,t){return e.tagName==="A"&&(t.getEventType()===_.CLICK||t.getEventType()===_.CLICKMOD)}var dh=Symbol.for("propagationStopped"),Qc={REPLAY:101};var kC="`preventDefault` called during event replay.";var FC="`composedPath` called during event replay.",Gi=class{dispatchDelegate;clickModSupport;actionResolver;dispatcher;constructor(t,n=!0){this.dispatchDelegate=t,this.clickModSupport=n,this.actionResolver=new zc({clickModSupport:n}),this.dispatcher=new Gc(r=>{this.dispatchToDelegate(r)},{actionResolver:this.actionResolver})}dispatch(t){this.dispatcher.dispatch(t)}dispatchToDelegate(t){for(t.getIsReplay()&&jC(t),LC(t);t.getAction();){if(BC(t),Zc(t.getEventType())&&t.getAction().element!==t.getTargetElement()||(this.dispatchDelegate(t.getEvent(),t.getAction().name),VC(t)))return;this.actionResolver.resolveParentAction(t.eventInfo)}}};function LC(e){let t=e.getEvent(),n=e.getEvent().stopPropagation.bind(t),r=()=>{t[dh]=!0,n()};hn(t,"stopPropagation",r),hn(t,"stopImmediatePropagation",r)}function VC(e){return!!e.getEvent()[dh]}function jC(e){let t=e.getEvent(),n=e.getTargetElement(),r=t.preventDefault.bind(t);hn(t,"target",n),hn(t,"eventPhase",Qc.REPLAY),hn(t,"preventDefault",()=>{throw r(),new Error(kC+"")}),hn(t,"composedPath",()=>{throw new Error(FC+"")})}function BC(e){let t=e.getEvent(),n=e.getAction()?.element;n&&hn(t,"currentTarget",n,{configurable:!0})}function hn(e,t,n,{configurable:r=!1}={}){Object.defineProperty(e,t,{value:n,configurable:r})}function fh(e,t){e.ecrd(n=>{t.dispatch(n)},uh.I_AM_THE_JSACTION_FRAMEWORK)}function UC(e){return e?.q??[]}function $C(e){e&&(Xf(e.c,e.et,e.h),Xf(e.c,e.etc,e.h,!0))}function Xf(e,t,n,r){for(let o=0;o<t.length;o++)e.removeEventListener(t[o],n,r)}var HC=!1,hh=(()=>{class e{static MOUSE_SPECIAL_SUPPORT=HC;containerManager;eventHandlers={};browserEventTypeToExtraEventTypes={};dispatcher=null;queuedEventInfos=[];constructor(n){this.containerManager=n}handleEvent(n,r,o){let i=NC(n,r,r.target,o,Date.now());this.handleEventInfo(i)}handleEventInfo(n){if(!this.dispatcher){sh(n,!0),this.queuedEventInfos?.push(n);return}this.dispatcher(n)}addEvent(n,r,o){if(n in this.eventHandlers||!this.containerManager||!e.MOUSE_SPECIAL_SUPPORT&&pC.indexOf(n)>=0)return;let i=(a,c,l)=>{this.handleEvent(a,c,l)};this.eventHandlers[n]=i;let s=mC(r||n);if(s!==n){let a=this.browserEventTypeToExtraEventTypes[s]||[];a.push(n),this.browserEventTypeToExtraEventTypes[s]=a}this.containerManager.addEventListener(s,a=>c=>{i(n,c,a)},o)}replayEarlyEvents(n=window._ejsa){n&&(this.replayEarlyEventInfos(n.q),$C(n),delete window._ejsa)}replayEarlyEventInfos(n){for(let r=0;r<n.length;r++){let o=n[r],i=this.getEventTypesForBrowserEventType(o.eventType);for(let s=0;s<i.length;s++){let a=lh(o);Yc(a,i[s]),this.handleEventInfo(a)}}}getEventTypesForBrowserEventType(n){let r=[];return this.eventHandlers[n]&&r.push(n),this.browserEventTypeToExtraEventTypes[n]&&r.push(...this.browserEventTypeToExtraEventTypes[n]),r}handler(n){return this.eventHandlers[n]}cleanUp(){this.containerManager?.cleanUp(),this.containerManager=null,this.eventHandlers={},this.browserEventTypeToExtraEventTypes={},this.dispatcher=null,this.queuedEventInfos=[]}registerDispatcher(n,r){this.ecrd(n,r)}ecrd(n,r){if(this.dispatcher=n,this.queuedEventInfos?.length){for(let o=0;o<this.queuedEventInfos.length;o++)this.handleEventInfo(this.queuedEventInfos[o]);this.queuedEventInfos=null}}}return e})();function ph(e,t=window){return UC(t._ejsas?.[e])}function Kc(e,t=window){t._ejsas&&(t._ejsas[e]=void 0)}var zC="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",D=class extends Error{code;constructor(t,n){super(pu(t,n)),this.code=t}};function GC(e){return`NG0${Math.abs(e)}`}function pu(e,t){return`${GC(e)}${t?": "+t:""}`}var hp=Symbol("InputSignalNode#UNSET"),qC=O(m({},gi),{transformFn:void 0,applyValueToInputSignal(e,t){Zr(e,t)}});function pp(e,t){let n=Object.create(qC);n.value=e,n.transformFn=t?.transform;function r(){if(Wr(n),n.value===hp){let o=null;throw new D(-950,o)}return n.value}return r[$e]=n,r}function uo(e){return{toString:e}.toString()}var qi="__parameters__";function WC(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function gp(e,t,n){return uo(()=>{let r=WC(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let f=c.hasOwnProperty(qi)?c[qi]:Object.defineProperty(c,qi,{value:[]})[qi];for(;f.length<=u;)f.push(null);return(f[u]=f[u]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var tr=globalThis;function G(e){for(let t in e)if(e[t]===G)return t;throw Error("Could not find renamed property on target object.")}function ZC(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Me(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Me).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function ml(e,t){return e?t?`${e} ${t}`:e:t||""}var YC=G({__forward_ref__:G});function Tn(e){return e.__forward_ref__=Tn,e.toString=function(){return Me(this())},e}function ve(e){return mp(e)?e():e}function mp(e){return typeof e=="function"&&e.hasOwnProperty(YC)&&e.__forward_ref__===Tn}function C(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Qt(e){return{providers:e.providers||[],imports:e.imports||[]}}function Ns(e){return gh(e,yp)||gh(e,Dp)}function vp(e){return Ns(e)!==null}function gh(e,t){return e.hasOwnProperty(t)?e[t]:null}function QC(e){let t=e&&(e[yp]||e[Dp]);return t||null}function mh(e){return e&&(e.hasOwnProperty(vh)||e.hasOwnProperty(KC))?e[vh]:null}var yp=G({\u0275prov:G}),vh=G({\u0275inj:G}),Dp=G({ngInjectableDef:G}),KC=G({ngInjectorDef:G}),y=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=C({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Cp(e){return e&&!!e.\u0275providers}var JC=G({\u0275cmp:G}),XC=G({\u0275dir:G}),eE=G({\u0275pipe:G}),tE=G({\u0275mod:G}),os=G({\u0275fac:G}),to=G({__NG_ELEMENT_ID__:G}),yh=G({__NG_ENV_ID__:G});function Ep(e){return typeof e=="string"?e:e==null?"":String(e)}function nE(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Ep(e)}function wp(e,t){throw new D(-200,e)}function gu(e,t){throw new D(-201,!1)}var x=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(x||{}),vl;function _p(){return vl}function be(e){let t=vl;return vl=e,t}function bp(e,t,n){let r=Ns(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&x.Optional)return null;if(t!==void 0)return t;gu(e,"Injector")}var rE={},gn=rE,yl="__NG_DI_FLAG__",is=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?mi:gn,r)}},ss="ngTempTokenPath",oE="ngTokenPath",iE=/\n/gm,sE="\u0275",Dh="__source";function aE(e,t=x.Default){if(Yr()===void 0)throw new D(-203,!1);if(Yr()===null)return bp(e,void 0,t);{let n=Yr(),r;return n instanceof is?r=n.injector:r=n,r.get(e,t&x.Optional?null:void 0,t)}}function w(e,t=x.Default){return(_p()||aE)(ve(e),t)}function g(e,t=x.Default){return w(e,xs(t))}function xs(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Dl(e){let t=[];for(let n=0;n<e.length;n++){let r=ve(e[n]);if(Array.isArray(r)){if(r.length===0)throw new D(900,!1);let o,i=x.Default;for(let s=0;s<r.length;s++){let a=r[s],c=cE(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(w(o,i))}else t.push(w(r))}return t}function Ip(e,t){return e[yl]=t,e.prototype[yl]=t,e}function cE(e){return e[yl]}function lE(e,t,n,r){let o=e[ss];throw t[Dh]&&o.unshift(t[Dh]),e.message=uE(`
`+e.message,o,n,r),e[oE]=o,e[ss]=null,e}function uE(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==sE?e.slice(2):e;let o=Me(t);if(Array.isArray(t))o=t.map(Me).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Me(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(iE,`
  `)}`}var Mp=Ip(gp("Optional"),8);var dE=Ip(gp("SkipSelf"),4);function vn(e,t){let n=e.hasOwnProperty(os);return n?e[os]:null}function fE(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function hE(e){return e.flat(Number.POSITIVE_INFINITY)}function mu(e,t){e.forEach(n=>Array.isArray(n)?mu(n,t):t(n))}function Sp(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function as(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function pE(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function vu(e,t,n){let r=fo(e,t);return r>=0?e[r|1]=n:(r=~r,pE(e,r,t,n)),r}function Jc(e,t){let n=fo(e,t);if(n>=0)return e[n|1]}function fo(e,t){return gE(e,t,1)}function gE(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var yn={},Ie=[],Zt=new y(""),Tp=new y("",-1),Ap=new y(""),cs=class{get(t,n=gn){if(n===gn){let r=new Error(`NullInjectorError: No provider for ${Me(t)}!`);throw r.name="NullInjectorError",r}return n}};function Np(e,t){let n=e[tE]||null;if(!n&&t===!0)throw new Error(`Type ${Me(e)} does not have '\u0275mod' property.`);return n}function Dn(e){return e[JC]||null}function xp(e){return e[XC]||null}function Rp(e){return e[eE]||null}function Kt(e){return{\u0275providers:e}}function mE(...e){return{\u0275providers:yu(!0,e),\u0275fromNgModule:!0}}function yu(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return mu(t,s=>{let a=s;Cl(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Op(o,i),n}function Op(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Du(o,i=>{t(i,r)})}}function Cl(e,t,n,r){if(e=ve(e),!e)return!1;let o=null,i=mh(e),s=!i&&Dn(e);if(!i&&!s){let c=e.ngModule;if(i=mh(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Cl(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{mu(i.imports,u=>{Cl(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&Op(l,t)}if(!a){let l=vn(o)||(()=>new o);t({provide:o,useFactory:l,deps:Ie},o),t({provide:Ap,useValue:o,multi:!0},o),t({provide:Zt,useValue:()=>w(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;Du(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function Du(e,t){for(let n of e)Cp(n)&&(n=n.\u0275providers),Array.isArray(n)?Du(n,t):t(n)}var vE=G({provide:String,useValue:G});function Pp(e){return e!==null&&typeof e=="object"&&vE in e}function yE(e){return!!(e&&e.useExisting)}function DE(e){return!!(e&&e.useFactory)}function dr(e){return typeof e=="function"}function CE(e){return!!e.useClass}var Rs=new y(""),Qi={},Ch={},Xc;function Cu(){return Xc===void 0&&(Xc=new cs),Xc}var ye=class{},no=class extends ye{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,wl(t,s=>this.processProvider(s)),this.records.set(Tp,nr(void 0,this)),o.has("environment")&&this.records.set(ye,nr(void 0,this));let i=this.records.get(Rs);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Ap,Ie,x.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?mi:gn,r)}destroy(){Xr(this),this._destroyed=!0;let t=k(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),k(t)}}onDestroy(t){return Xr(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Xr(this);let n=bt(this),r=be(void 0),o;try{return t()}finally{bt(n),be(r)}}get(t,n=gn,r=x.Default){if(Xr(this),t.hasOwnProperty(yh))return t[yh](this);r=xs(r);let o,i=bt(this),s=be(void 0);try{if(!(r&x.SkipSelf)){let c=this.records.get(t);if(c===void 0){let l=IE(t)&&Ns(t);l&&this.injectableDefInScope(l)?c=nr(El(t),Qi):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&x.Self?Cu():this.parent;return n=r&x.Optional&&n===gn?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[ss]=a[ss]||[]).unshift(Me(t)),i)throw a;return lE(a,t,"R3InjectorError",this.source)}else throw a}finally{be(s),bt(i)}}resolveInjectorInitializers(){let t=k(null),n=bt(this),r=be(void 0),o;try{let i=this.get(Zt,Ie,x.Self);for(let s of i)s()}finally{bt(n),be(r),k(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(Me(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=ve(t);let n=dr(t)?t:ve(t&&t.provide),r=wE(t);if(!dr(t)&&t.multi===!0){let o=this.records.get(n);o||(o=nr(void 0,Qi,!0),o.factory=()=>Dl(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=k(null);try{return n.value===Ch?wp(Me(t)):n.value===Qi&&(n.value=Ch,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&bE(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{k(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ve(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function El(e){let t=Ns(e),n=t!==null?t.factory:vn(e);if(n!==null)return n;if(e instanceof y)throw new D(204,!1);if(e instanceof Function)return EE(e);throw new D(204,!1)}function EE(e){if(e.length>0)throw new D(204,!1);let n=QC(e);return n!==null?()=>n.factory(e):()=>new e}function wE(e){if(Pp(e))return nr(void 0,e.useValue);{let t=kp(e);return nr(t,Qi)}}function kp(e,t,n){let r;if(dr(e)){let o=ve(e);return vn(o)||El(o)}else if(Pp(e))r=()=>ve(e.useValue);else if(DE(e))r=()=>e.useFactory(...Dl(e.deps||[]));else if(yE(e))r=(o,i)=>w(ve(e.useExisting),i!==void 0&&i&x.Optional?x.Optional:void 0);else{let o=ve(e&&(e.useClass||e.provide));if(_E(e))r=()=>new o(...Dl(e.deps));else return vn(o)||El(o)}return r}function Xr(e){if(e.destroyed)throw new D(205,!1)}function nr(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function _E(e){return!!e.deps}function bE(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function IE(e){return typeof e=="function"||typeof e=="object"&&e instanceof y}function wl(e,t){for(let n of e)Array.isArray(n)?wl(n,t):n&&Cp(n)?wl(n.\u0275providers,t):t(n)}function Le(e,t){let n;e instanceof no?(Xr(e),n=e):n=new is(e);let r,o=bt(n),i=be(void 0);try{return t()}finally{bt(o),be(i)}}function Fp(){return _p()!==void 0||Yr()!=null}function Lp(e){if(!Fp())throw new D(-203,!1)}function ME(e){return typeof e=="function"}var Fe=0,M=1,S=2,le=3,nt=4,it=5,ft=6,ls=7,Te=8,ht=9,Yt=10,Q=11,ro=12,Eh=13,mr=14,ze=15,Cn=16,rr=17,Nt=18,Os=19,Vp=20,Wt=21,el=22,us=23,He=24,ar=25,ue=26,jp=1,xt=6,Rt=7,ds=8,fr=9,Se=10;function rt(e){return Array.isArray(e)&&typeof e[jp]=="object"}function yt(e){return Array.isArray(e)&&e[jp]===!0}function Bp(e){return(e.flags&4)!==0}function An(e){return e.componentOffset>-1}function Eu(e){return(e.flags&1)===1}function pt(e){return!!e.template}function oo(e){return(e[S]&512)!==0}function Nn(e){return(e[S]&256)===256}var _l=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Up(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var ho=(()=>{let e=()=>$p;return e.ngInherit=!0,e})();function $p(e){return e.type.prototype.ngOnChanges&&(e.setInput=TE),SE}function SE(){let e=zp(this),t=e?.current;if(t){let n=e.previous;if(n===yn)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function TE(e,t,n,r,o){let i=this.declaredInputs[r],s=zp(e)||AE(e,{previous:yn,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new _l(l&&l.currentValue,n,c===yn),Up(e,t,o,n)}var Hp="__ngSimpleChanges__";function zp(e){return e[Hp]||null}function AE(e,t){return e[Hp]=t}var wh=null;var $=function(e,t=null,n){wh?.(e,t,n)},NE="svg",xE="math";function ot(e){for(;Array.isArray(e);)e=e[Fe];return e}function Gp(e,t){return ot(t[e])}function Dt(e,t){return ot(t[e.index])}function Ps(e,t){return e.data[t]}function RE(e,t){return e[t]}function OE(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function gt(e,t){let n=t[e];return rt(n)?n:n[Fe]}function PE(e){return(e[S]&4)===4}function wu(e){return(e[S]&128)===128}function kE(e){return yt(e[le])}function fs(e,t){return t==null?null:e[t]}function qp(e){e[rr]=0}function Wp(e){e[S]&1024||(e[S]|=1024,wu(e)&&po(e))}function FE(e,t){for(;e>0;)t=t[mr],e--;return t}function ks(e){return!!(e[S]&9216||e[He]?.dirty)}function bl(e){e[Yt].changeDetectionScheduler?.notify(8),e[S]&64&&(e[S]|=1024),ks(e)&&po(e)}function po(e){e[Yt].changeDetectionScheduler?.notify(0);let t=En(e);for(;t!==null&&!(t[S]&8192||(t[S]|=8192,!wu(t)));)t=En(t)}function Zp(e,t){if(Nn(e))throw new D(911,!1);e[Wt]===null&&(e[Wt]=[]),e[Wt].push(t)}function LE(e,t){if(e[Wt]===null)return;let n=e[Wt].indexOf(t);n!==-1&&e[Wt].splice(n,1)}function En(e){let t=e[le];return yt(t)?t[le]:t}function _u(e){return e[ls]??=[]}function bu(e){return e.cleanup??=[]}function VE(e,t,n,r){let o=_u(t);o.push(n),e.firstCreatePass&&bu(e).push(r,o.length-1)}var R={lFrame:rg(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Il=!1;function jE(){return R.lFrame.elementDepthCount}function BE(){R.lFrame.elementDepthCount++}function UE(){R.lFrame.elementDepthCount--}function Yp(){return R.bindingsEnabled}function go(){return R.skipHydrationRootTNode!==null}function $E(e){return R.skipHydrationRootTNode===e}function HE(e){R.skipHydrationRootTNode=e}function zE(){R.skipHydrationRootTNode=null}function H(){return R.lFrame.lView}function ge(){return R.lFrame.tView}function qe(e){return R.lFrame.contextLView=e,e[Te]}function We(e){return R.lFrame.contextLView=null,e}function Ve(){let e=Qp();for(;e!==null&&e.type===64;)e=e.parent;return e}function Qp(){return R.lFrame.currentTNode}function GE(){let e=R.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function mo(e,t){let n=R.lFrame;n.currentTNode=e,n.isParent=t}function Kp(){return R.lFrame.isParent}function qE(){R.lFrame.isParent=!1}function Jp(){return Il}function _h(e){let t=Il;return Il=e,t}function WE(){let e=R.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function ZE(e){return R.lFrame.bindingIndex=e}function Iu(){return R.lFrame.bindingIndex++}function Xp(e){let t=R.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function YE(){return R.lFrame.inI18n}function QE(e,t){let n=R.lFrame;n.bindingIndex=n.bindingRootIndex=e,Ml(t)}function KE(){return R.lFrame.currentDirectiveIndex}function Ml(e){R.lFrame.currentDirectiveIndex=e}function JE(e){let t=R.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function eg(){return R.lFrame.currentQueryIndex}function Mu(e){R.lFrame.currentQueryIndex=e}function XE(e){let t=e[M];return t.type===2?t.declTNode:t.type===1?e[it]:null}function tg(e,t,n){if(n&x.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&x.Host);)if(o=XE(i),o===null||(i=i[mr],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=R.lFrame=ng();return r.currentTNode=t,r.lView=e,!0}function Su(e){let t=ng(),n=e[M];R.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function ng(){let e=R.lFrame,t=e===null?null:e.child;return t===null?rg(e):t}function rg(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function og(){let e=R.lFrame;return R.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var ig=og;function Tu(){let e=og();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function ew(e){return(R.lFrame.contextLView=FE(e,R.lFrame.contextLView))[Te]}function xn(){return R.lFrame.selectedIndex}function wn(e){R.lFrame.selectedIndex=e}function sg(){let e=R.lFrame;return Ps(e.tView,e.selectedIndex)}function ag(){return R.lFrame.currentNamespace}var cg=!0;function Au(){return cg}function Jt(e){cg=e}function tw(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=$p(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function lg(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function Ki(e,t,n){ug(e,t,3,n)}function Ji(e,t,n,r){(e[S]&3)===n&&ug(e,t,n,r)}function tl(e,t){let n=e[S];(n&3)===t&&(n&=16383,n+=1,e[S]=n)}function ug(e,t,n,r){let o=r!==void 0?e[rr]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[rr]+=65536),(a<i||i==-1)&&(nw(e,n,t,c),e[rr]=(e[rr]&**********)+c+2),c++}function bh(e,t){$(4,e,t);let n=k(null);try{t.call(e)}finally{k(n),$(5,e,t)}}function nw(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[S]>>14<e[rr]>>16&&(e[S]&3)===t&&(e[S]+=16384,bh(a,i)):bh(a,i)}var cr=-1,_n=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function rw(e){return(e.flags&8)!==0}function ow(e){return(e.flags&16)!==0}function iw(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];aw(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function sw(e){return e===3||e===4||e===6}function aw(e){return e.charCodeAt(0)===64}function io(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Ih(e,n,o,null,t[++r]):Ih(e,n,o,null,null))}}return e}function Ih(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function dg(e){return e!==cr}function hs(e){return e&32767}function cw(e){return e>>16}function ps(e,t){let n=cw(e),r=t;for(;n>0;)r=r[mr],n--;return r}var Sl=!0;function gs(e){let t=Sl;return Sl=e,t}var lw=256,fg=lw-1,hg=5,uw=0,dt={};function dw(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(to)&&(r=n[to]),r==null&&(r=n[to]=uw++);let o=r&fg,i=1<<o;t.data[e+(o>>hg)]|=i}function ms(e,t){let n=pg(e,t);if(n!==-1)return n;let r=t[M];r.firstCreatePass&&(e.injectorIndex=t.length,nl(r.data,e),nl(t,null),nl(r.blueprint,null));let o=Nu(e,t),i=e.injectorIndex;if(dg(o)){let s=hs(o),a=ps(o,t),c=a[M].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function nl(e,t){e.push(0,0,0,0,0,0,0,0,t)}function pg(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Nu(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Dg(o),r===null)return cr;if(n++,o=o[mr],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return cr}function Tl(e,t,n){dw(e,t,n)}function gg(e,t,n){if(n&x.Optional||e!==void 0)return e;gu(t,"NodeInjector")}function mg(e,t,n,r){if(n&x.Optional&&r===void 0&&(r=null),(n&(x.Self|x.Host))===0){let o=e[ht],i=be(void 0);try{return o?o.get(t,r,n&x.Optional):bp(t,r,n&x.Optional)}finally{be(i)}}return gg(r,t,n)}function vg(e,t,n,r=x.Default,o){if(e!==null){if(t[S]&2048&&!(r&x.Self)){let s=gw(e,t,n,r,dt);if(s!==dt)return s}let i=yg(e,t,n,r,dt);if(i!==dt)return i}return mg(t,n,r,o)}function yg(e,t,n,r,o){let i=hw(n);if(typeof i=="function"){if(!tg(t,e,r))return r&x.Host?gg(o,n,r):mg(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&x.Optional))gu(n);else return s}finally{ig()}}else if(typeof i=="number"){let s=null,a=pg(e,t),c=cr,l=r&x.Host?t[ze][it]:null;for((a===-1||r&x.SkipSelf)&&(c=a===-1?Nu(e,t):t[a+8],c===cr||!Sh(r,!1)?a=-1:(s=t[M],a=hs(c),t=ps(c,t)));a!==-1;){let u=t[M];if(Mh(i,a,u.data)){let f=fw(a,t,n,s,r,l);if(f!==dt)return f}c=t[a+8],c!==cr&&Sh(r,t[M].data[a+8]===l)&&Mh(i,a,t)?(s=u,a=hs(c),t=ps(c,t)):a=-1}}return o}function fw(e,t,n,r,o,i){let s=t[M],a=s.data[e+8],c=r==null?An(a)&&Sl:r!=s&&(a.type&3)!==0,l=o&x.Host&&i===a,u=Xi(a,s,n,c,l);return u!==null?so(t,s,u,a,o):dt}function Xi(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,f=r?a:a+u,h=o?a+u:l;for(let d=f;d<h;d++){let p=s[d];if(d<c&&n===p||d>=c&&p.type===n)return d}if(o){let d=s[c];if(d&&pt(d)&&d.type===n)return c}return null}function so(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof _n){let a=i;a.resolving&&wp(nE(s[n]));let c=gs(a.canSeeViewProviders);a.resolving=!0;let l,u=a.injectImpl?be(a.injectImpl):null,f=tg(e,r,x.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&tw(n,s[n],t)}finally{u!==null&&be(u),gs(c),a.resolving=!1,ig()}}return i}function hw(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(to)?e[to]:void 0;return typeof t=="number"?t>=0?t&fg:pw:t}function Mh(e,t,n){let r=1<<e;return!!(n[t+(e>>hg)]&r)}function Sh(e,t){return!(e&x.Self)&&!(e&x.Host&&t)}var mn=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return vg(this._tNode,this._lView,t,xs(r),n)}};function pw(){return new mn(Ve(),H())}function Rn(e){return uo(()=>{let t=e.prototype.constructor,n=t[os]||Al(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[os]||Al(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Al(e){return mp(e)?()=>{let t=Al(ve(e));return t&&t()}:vn(e)}function gw(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[S]&2048&&!oo(s);){let a=yg(i,s,n,r|x.Self,dt);if(a!==dt)return a;let c=i.parent;if(!c){let l=s[Vp];if(l){let u=l.get(n,dt,r);if(u!==dt)return u}c=Dg(s),s=s[mr]}i=c}return o}function Dg(e){let t=e[M],n=t.type;return n===2?t.declTNode:n===1?e[it]:null}function Th(e,t=null,n=null,r){let o=Cg(e,t,n,r);return o.resolveInjectorInitializers(),o}function Cg(e,t=null,n=null,r,o=new Set){let i=[n||Ie,mE(e)];return r=r||(typeof e=="object"?void 0:Me(e)),new no(i,t||Cu(),r||null,o)}var Ge=class e{static THROW_IF_NOT_FOUND=gn;static NULL=new cs;static create(t,n){if(Array.isArray(t))return Th({name:""},n,t,"");{let r=t.name??"";return Th({name:r},t.parent,t.providers,r)}}static \u0275prov=C({token:e,providedIn:"any",factory:()=>w(Tp)});static __NG_ELEMENT_ID__=-1};var mw=new y("");mw.__NG_ELEMENT_ID__=e=>{let t=Ve();if(t===null)throw new D(204,!1);if(t.type&2)return t.value;if(e&x.Optional)return null;throw new D(204,!1)};var Eg=!1,vr=(()=>{class e{static __NG_ELEMENT_ID__=vw;static __NG_ENV_ID__=n=>n}return e})(),Nl=class extends vr{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return Nn(n)?(t(),()=>{}):(Zp(n,t),()=>LE(n,t))}};function vw(){return new Nl(H())}var bn=class{},Fs=new y("",{providedIn:"root",factory:()=>!1});var wg=new y(""),_g=new y(""),st=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new X(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=C({token:e,providedIn:"root",factory:()=>new e})}return e})(),yw=(()=>{class e{internalPendingTasks=g(st);scheduler=g(bn);add(){let n=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(n)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(n))}}run(n){return Ut(this,null,function*(){let r=this.add();try{return yield n()}finally{r()}})}static \u0275prov=C({token:e,providedIn:"root",factory:()=>new e})}return e})(),xl=class extends W{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Fp()&&(this.destroyRef=g(vr,{optional:!0})??void 0,this.pendingTasks=g(st,{optional:!0})??void 0)}emit(t){let n=k(null);try{super.next(t)}finally{k(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof ne&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},de=xl;function vs(...e){}function bg(e){let t,n;function r(){e=vs;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Ah(e){return queueMicrotask(()=>e()),()=>{e=vs}}var xu="isAngularZone",ys=xu+"_ID",Dw=0,ee=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new de(!1);onMicrotaskEmpty=new de(!1);onStable=new de(!1);onError=new de(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Eg}=t;if(typeof Zone>"u")throw new D(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,ww(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(xu)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new D(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new D(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Cw,vs,vs);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Cw={};function Ru(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Ew(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){bg(()=>{e.callbackScheduled=!1,Rl(e),e.isCheckStableRunning=!0,Ru(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Rl(e)}function ww(e){let t=()=>{Ew(e)},n=Dw++;e._inner=e._inner.fork({name:"angular",properties:{[xu]:!0,[ys]:n,[ys+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(_w(c))return r.invokeTask(i,s,a,c);try{return Nh(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),xh(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return Nh(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!bw(c)&&t(),xh(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Rl(e),Ru(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Rl(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Nh(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function xh(e){e._nesting--,Ru(e)}var Ol=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new de;onMicrotaskEmpty=new de;onStable=new de;onError=new de;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function _w(e){return Ig(e,"__ignore_ng_zone__")}function bw(e){return Ig(e,"__scheduler_tick__")}function Ig(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var mt=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Iw=new y("",{providedIn:"root",factory:()=>{let e=g(ee),t=g(mt);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Rh(e,t){return pp(e,t)}function Mw(e){return pp(hp,e)}var Mg=(Rh.required=Mw,Rh);function Sw(){return yr(Ve(),H())}function yr(e,t){return new Ct(Dt(e,t))}var Ct=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Sw}return e})();function Tw(e){return e instanceof Ct?e.nativeElement:e}function Aw(e){return typeof e=="function"&&e[$e]!==void 0}function vo(e,t){let n=bc(e,t?.equal),r=n[$e];return n.set=o=>Zr(r,o),n.update=o=>Ic(r,o),n.asReadonly=Nw.bind(n),n}function Nw(){let e=this[$e];if(e.readonlyFn===void 0){let t=()=>this();t[$e]=e,e.readonlyFn=t}return e.readonlyFn}function Sg(e){return Aw(e)&&typeof e.set=="function"}function xw(){return this._results[Symbol.iterator]()}var Pl=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new W}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=hE(t);(this._changesDetected=!fE(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=xw},Rw="ngSkipHydration",Ow="ngskiphydration";function Tg(e){let t=e.mergedAttrs;if(t===null)return!1;for(let n=0;n<t.length;n+=2){let r=t[n];if(typeof r=="number")return!1;if(typeof r=="string"&&r.toLowerCase()===Ow)return!0}return!1}function Ag(e){return e.hasAttribute(Rw)}function Ds(e){return(e.flags&128)===128}function Pw(e){if(Ds(e))return!0;let t=e.parent;for(;t;){if(Ds(e)||Tg(t))return!0;t=t.parent}return!1}var Ng=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Ng||{}),xg=new Map,kw=0;function Fw(){return kw++}function Lw(e){xg.set(e[Os],e)}function kl(e){xg.delete(e[Os])}var Oh="__ngContext__";function yo(e,t){rt(t)?(e[Oh]=t[Os],Lw(t)):e[Oh]=t}function Rg(e){return Pg(e[ro])}function Og(e){return Pg(e[nt])}function Pg(e){for(;e!==null&&!yt(e);)e=e[nt];return e}var Fl;function kg(e){Fl=e}function Ls(){if(Fl!==void 0)return Fl;if(typeof document<"u")return document;throw new D(210,!1)}var At=new y("",{providedIn:"root",factory:()=>Vw}),Vw="ng",Ou=new y(""),at=new y("",{providedIn:"platform",factory:()=>"unknown"});var Pu=new y("",{providedIn:"root",factory:()=>Ls().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});function jw(){let e=new Dr;return e.store=Bw(Ls(),g(At)),e}var Dr=(()=>{class e{static \u0275prov=C({token:e,providedIn:"root",factory:jw});store={};onSerializeCallbacks={};get(n,r){return this.store[n]!==void 0?this.store[n]:r}set(n,r){this.store[n]=r}remove(n){delete this.store[n]}hasKey(n){return this.store.hasOwnProperty(n)}get isEmpty(){return Object.keys(this.store).length===0}onSerialize(n,r){this.onSerializeCallbacks[n]=r}toJson(){for(let n in this.onSerializeCallbacks)if(this.onSerializeCallbacks.hasOwnProperty(n))try{this.store[n]=this.onSerializeCallbacks[n]()}catch(r){console.warn("Exception in onSerialize callback: ",r)}return JSON.stringify(this.store).replace(/</g,"\\u003C")}}return e})();function Bw(e,t){let n=e.getElementById(t+"-state");if(n?.textContent)try{return JSON.parse(n.textContent)}catch(r){console.warn("Exception while restoring TransferState for app "+t,r)}return{}}var Fg="h",Lg="b",Uw="f",$w="n",Hw="e",zw="t",ku="c",Vg="x",Cs="r",Gw="i",qw="n",jg="d";var Ww="di",Zw="s",Yw="p";var Wi=new y(""),Bg=!1,Ug=new y("",{providedIn:"root",factory:()=>Bg});var $g=new y(""),Qw=!1,Kw=new y(""),Ph=new y("",{providedIn:"root",factory:()=>new Map}),Fu=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Fu||{}),Cr=new y(""),kh=new Set;function Xt(e){kh.has(e)||(kh.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Hg=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=Jw}return e})();function Jw(){return new Hg(H(),Ve())}var or=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(or||{}),zg=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=C({token:e,providedIn:"root",factory:()=>new e})}return e})(),Xw=[or.EarlyRead,or.Write,or.MixedReadWrite,or.Read],e_=(()=>{class e{ngZone=g(ee);scheduler=g(bn);errorHandler=g(mt,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){g(Cr,{optional:!0})}execute(){let n=this.sequences.size>0;n&&$(16),this.executing=!0;for(let r of Xw)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&$(17)}register(n){let{view:r}=n;r!==void 0?((r[ar]??=[]).push(n),po(r),r[S]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Fu.AFTER_NEXT_RENDER,n):n()}static \u0275prov=C({token:e,providedIn:"root",factory:()=>new e})}return e})(),Ll=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[ar];t&&(this.view[ar]=t.filter(n=>n!==this))}};function Lu(e,t){!t?.injector&&Lp(Lu);let n=t?.injector??g(Ge);return Xt("NgAfterNextRender"),n_(e,n,t,!0)}function t_(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function n_(e,t,n,r){let o=t.get(zg);o.impl??=t.get(e_);let i=t.get(Cr,null,{optional:!0}),s=n?.phase??or.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(vr):null,c=t.get(Hg,null,{optional:!0}),l=new Ll(o.impl,t_(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(l),l}var ke=function(e){return e[e.NOT_STARTED=0]="NOT_STARTED",e[e.IN_PROGRESS=1]="IN_PROGRESS",e[e.COMPLETE=2]="COMPLETE",e[e.FAILED=3]="FAILED",e}(ke||{}),Fh=0,r_=1,ce=function(e){return e[e.Placeholder=0]="Placeholder",e[e.Loading=1]="Loading",e[e.Complete=2]="Complete",e[e.Error=3]="Error",e}(ce||{});var o_=0,Vs=1;var i_=4,s_=5;var a_=7,lr=8,c_=9,Gg=function(e){return e[e.Manual=0]="Manual",e[e.Playthrough=1]="Playthrough",e}(Gg||{});function es(e,t){let n=u_(e),r=t[n];if(r!==null){for(let o of r)o();t[n]=null}}function l_(e){es(1,e),es(0,e),es(2,e)}function u_(e){let t=i_;return e===1?t=s_:e===2&&(t=c_),t}function qg(e){return e+1}function Do(e,t){let n=e[M],r=qg(t.index);return e[r]}function js(e,t){let n=qg(t.index);return e.data[n]}function d_(e,t,n){let r=t[M],o=js(r,n);switch(e){case ce.Complete:return o.primaryTmplIndex;case ce.Loading:return o.loadingTmplIndex;case ce.Error:return o.errorTmplIndex;case ce.Placeholder:return o.placeholderTmplIndex;default:return null}}function Lh(e,t){return t===ce.Placeholder?e.placeholderBlockConfig?.[Fh]??null:t===ce.Loading?e.loadingBlockConfig?.[Fh]??null:null}function f_(e){return e.loadingBlockConfig?.[r_]??null}function Vh(e,t){if(!e||e.length===0)return t;let n=new Set(e);for(let r of t)n.add(r);return e.length===n.size?e:Array.from(n)}function h_(e,t){let n=t.primaryTmplIndex+ue;return Ps(e,n)}var Bs="ngb";var p_=(e,t,n)=>{let r=e,o=r.__jsaction_fns??new Map,i=o.get(t)??[];i.push(n),o.set(t,i),r.__jsaction_fns=o},g_=(e,t)=>{let n=e,r=n.getAttribute(Bs)??"",o=t.get(r)??new Set;o.has(n)||o.add(n),t.set(r,o)};var m_=e=>{e.removeAttribute(qc.JSACTION),e.removeAttribute(Bs),e.__jsaction_fns=void 0},v_=new y("",{providedIn:"root",factory:()=>({})});function Wg(e,t){let n=t?.__jsaction_fns?.get(e.type);if(!(!n||!t?.isConnected))for(let r of n)r(e)}var Vl=new Map;function y_(e,t){return Vl.set(e,t),()=>Vl.delete(e)}var jh=!1,Zg=(e,t,n,r)=>{};function D_(e,t,n,r){Zg(e,t,n,r)}function C_(){jh||(Zg=(e,t,n,r)=>{let o=e[ht].get(At);Vl.get(o)?.(t,n,r)},jh=!0)}var Vu=new y("");var E_="__nghData__",Yg=E_,w_="__nghDeferData__",__=w_,rl="ngh",b_="nghm",Qg=()=>null;function I_(e,t,n=!1){let r=e.getAttribute(rl);if(r==null)return null;let[o,i]=r.split("|");if(r=n?i:o,!r)return null;let s=i?`|${i}`:"",a=n?o:s,c={};if(r!==""){let u=t.get(Dr,null,{optional:!0});u!==null&&(c=u.get(Yg,[])[Number(r)])}let l={data:c,firstChild:e.firstChild??null};return n&&(l.firstChild=e,Us(l,0,e.nextSibling)),a?e.setAttribute(rl,a):e.removeAttribute(rl),l}function M_(){Qg=I_}function Kg(e,t,n=!1){return Qg(e,t,n)}function S_(e){let t=e._lView;return t[M].type===2?null:(oo(t)&&(t=t[ue]),t)}function T_(e){return e.textContent?.replace(/\s/gm,"")}function A_(e){let t=Ls(),n=t.createNodeIterator(e,NodeFilter.SHOW_COMMENT,{acceptNode(i){let s=T_(i);return s==="ngetn"||s==="ngtns"?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}}),r,o=[];for(;r=n.nextNode();)o.push(r);for(let i of o)i.textContent==="ngetn"?i.replaceWith(t.createTextNode("")):i.remove()}function Us(e,t,n){e.segmentHeads??={},e.segmentHeads[t]=n}function jl(e,t){return e.segmentHeads?.[t]??null}function N_(e){return e.get(Kw,!1,{optional:!0})}function x_(e,t){let n=e.data,r=n[Hw]?.[t]??null;return r===null&&n[ku]?.[t]&&(r=ju(e,t)),r}function Jg(e,t){return e.data[ku]?.[t]??null}function ju(e,t){let n=Jg(e,t)??[],r=0;for(let o of n)r+=o[Cs]*(o[Vg]??1);return r}function R_(e){if(typeof e.disconnectedNodes>"u"){let t=e.data[jg];e.disconnectedNodes=t?new Set(t):null}return e.disconnectedNodes}function Co(e,t){if(typeof e.disconnectedNodes>"u"){let n=e.data[jg];e.disconnectedNodes=n?new Set(n):null}return!!R_(e)?.has(t)}function O_(e,t){let n=t.get(Vu),o=t.get(Dr).get(__,{}),i=!1,s=e,a=null,c=[];for(;!i&&s;){i=n.has(s);let l=n.hydrating.get(s);if(a===null&&l!=null){a=l.promise;break}c.unshift(s),s=o[s][Yw]}return{parentBlockPromise:a,hydrationQueue:c}}function ol(e){return!!e&&e.nodeType===Node.COMMENT_NODE&&e.textContent?.trim()===b_}function Bh(e){for(;e&&e.nodeType===Node.TEXT_NODE;)e=e.previousSibling;return e}function P_(e){for(let r of e.body.childNodes)if(ol(r))return;let t=Bh(e.body.previousSibling);if(ol(t))return;let n=Bh(e.head.lastChild);if(!ol(n))throw new D(-507,!1)}function Xg(e,t){let n=e.contentQueries;if(n!==null){let r=k(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Mu(i),a.contentQueries(2,t[s],s)}}}finally{k(r)}}}function Bl(e,t,n){Mu(0);let r=k(null);try{t(e,n)}finally{k(r)}}function em(e,t,n){if(Bp(t)){let r=k(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{k(r)}}}var vt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(vt||{});var Ul=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${zC})`}};function Bu(e){return e instanceof Ul?e.changingThisBreaksApplicationSecurity:e}var k_=/^>|^->|<!--|-->|--!>|<!-$/g,F_=/(<|>)/g,L_="\u200B$1\u200B";function V_(e){return e.replace(k_,t=>t.replace(F_,L_))}function j_(e){return e.ownerDocument.body}function tm(e){return e instanceof Function?e():e}function B_(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var nm="ng-template";function U_(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&B_(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Uu(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Uu(e){return e.type===4&&e.value!==nm}function $_(e,t,n){let r=e.type===4&&!n?nm:e.value;return t===r}function H_(e,t,n){let r=4,o=e.attrs,i=o!==null?q_(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!et(r)&&!et(c))return!1;if(s&&et(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!$_(e,c,n)||c===""&&t.length===1){if(et(r))return!1;s=!0}}else if(r&8){if(o===null||!U_(e,o,c,n)){if(et(r))return!1;s=!0}}else{let l=t[++a],u=z_(c,o,Uu(e),n);if(u===-1){if(et(r))return!1;s=!0;continue}if(l!==""){let f;if(u>i?f="":f=o[u+1].toLowerCase(),r&2&&l!==f){if(et(r))return!1;s=!0}}}}return et(r)||s}function et(e){return(e&1)===0}function z_(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return W_(t,e)}function G_(e,t,n=!1){for(let r=0;r<t.length;r++)if(H_(e,t[r],n))return!0;return!1}function q_(e){for(let t=0;t<e.length;t++){let n=e[t];if(sw(n))return t}return e.length}function W_(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Uh(e,t){return e?":not("+t.trim()+")":t}function Z_(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!et(s)&&(t+=Uh(i,o),o=""),r=s,i=i||!et(r);n++}return o!==""&&(t+=Uh(i,o)),t}function Y_(e){return e.map(Z_).join(",")}function Q_(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!et(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Pt={};function rm(e,t){return e.createText(t)}function K_(e,t,n){e.setValue(t,n)}function om(e,t){return e.createComment(V_(t))}function $u(e,t,n){return e.createElement(t,n)}function Es(e,t,n,r,o){e.insertBefore(t,n,r,o)}function im(e,t,n){e.appendChild(t,n)}function $h(e,t,n,r,o){r!==null?Es(e,t,n,r,o):im(e,t,n)}function Hu(e,t,n){e.removeChild(null,t,n)}function sm(e){e.textContent=""}function J_(e,t,n){e.setAttribute(t,"style",n)}function X_(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function am(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&iw(e,t,r),o!==null&&X_(e,t,o),i!==null&&J_(e,t,i)}function zu(e,t,n,r,o,i,s,a,c,l,u){let f=ue+r,h=f+o,d=eb(f,h),p=typeof l=="function"?l():l;return d[M]={type:e,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:t,data:d.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:p,incompleteFirstPass:!1,ssrId:u}}function eb(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Pt);return n}function tb(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=zu(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Gu(e,t,n,r,o,i,s,a,c,l,u){let f=t.blueprint.slice();return f[Fe]=o,f[S]=r|4|128|8|64|1024,(l!==null||e&&e[S]&2048)&&(f[S]|=2048),qp(f),f[le]=f[mr]=e,f[Te]=n,f[Yt]=s||e&&e[Yt],f[Q]=a||e&&e[Q],f[ht]=c||e&&e[ht]||null,f[it]=i,f[Os]=Fw(),f[ft]=u,f[Vp]=l,f[ze]=t.type==2?e[ze]:f,f}function nb(e,t,n){let r=Dt(t,e),o=tb(n),i=e[Yt].rendererFactory,s=qu(e,Gu(e,o,null,cm(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function cm(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function lm(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function qu(e,t){return e[ro]?e[Eh][nt]=t:e[ro]=t,e[Eh]=t,t}function q(e=1){um(ge(),H(),xn()+e,!1)}function um(e,t,n,r){if(!r)if((t[S]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Ki(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Ji(t,i,0,n)}wn(n)}var $s=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}($s||{});function $l(e,t,n,r){let o=k(null);try{let[i,s,a]=e.inputs[n],c=null;(s&$s.SignalBased)!==0&&(c=t[i][$e]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Up(t,c,i,r)}finally{k(o)}}function dm(e,t,n,r,o){let i=xn(),s=r&2;try{wn(-1),s&&t.length>ue&&um(e,t,ue,!1),$(s?2:0,o),n(r,o)}finally{wn(i),$(s?3:1,o)}}function Wu(e,t,n){lb(e,t,n),(n.flags&64)===64&&ub(e,t,n)}function fm(e,t,n=Dt){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function rb(e,t,n,r){let i=r.get(Ug,Bg)||n===vt.ShadowDom,s=e.selectRootElement(t,i);return ob(s),s}function ob(e){hm(e)}var hm=()=>null;function ib(e){Ag(e)?sm(e):A_(e)}function sb(){hm=ib}function ab(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function pm(e,t,n,r,o,i,s,a){if(!a&&Yu(t,e,n,r,o)){An(t)&&cb(n,t.index);return}if(t.type&3){let c=Dt(t,n);r=ab(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function cb(e,t){let n=gt(t,e);n[S]&16||(n[S]|=64)}function lb(e,t,n){let r=n.directiveStart,o=n.directiveEnd;An(n)&&nb(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||ms(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=so(t,e,s,n);if(yo(c,t),i!==null&&fb(t,s-r,c,a,n,i),pt(a)){let l=gt(n.index,t);l[Te]=so(t,e,s,n)}}}function ub(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=KE();try{wn(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];Ml(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&db(c,l)}}finally{wn(-1),Ml(s)}}function db(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function gm(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];G_(t,i.selectors,!1)&&(r??=[],pt(i)?r.unshift(i):r.push(i))}return r}function fb(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];$l(r,n,c,l)}}function Zu(e,t){let n=e[ht],r=n?n.get(mt,null):null;r&&r.handleError(t)}function Yu(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],f=t.data[l];$l(f,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];$l(u,l,r,o),a=!0}return a}function hb(e,t){let n=gt(t,e),r=n[M];pb(r,n);let o=n[Fe];o!==null&&n[ft]===null&&(n[ft]=Kg(o,n[ht])),$(18),Qu(r,n,n[Te]),$(19,n[Te])}function pb(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Qu(e,t,n){Su(t);try{let r=e.viewQuery;r!==null&&Bl(1,r,n);let o=e.template;o!==null&&dm(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Nt]?.finishViewCreation(e),e.staticContentQueries&&Xg(e,t),e.staticViewQueries&&Bl(2,e.viewQuery,n);let i=e.components;i!==null&&gb(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[S]&=-5,Tu()}}function gb(e,t){for(let n=0;n<t.length;n++)hb(e,t[n])}function mm(e,t,n,r){let o=k(null);try{let i=t.tView,a=e[S]&4096?4096:16,c=Gu(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[Cn]=l;let u=e[Nt];return u!==null&&(c[Nt]=u.createEmbeddedView(i)),Qu(i,c,n),c}finally{k(o)}}function Hl(e,t){return!t||t.firstChild===null||Ds(e)}var mb;function Ku(e,t){return mb(e,t)}var Ot=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Ot||{});function Er(e){return(e.flags&32)===32}function ir(e,t,n,r,o){if(r!=null){let i,s=!1;yt(r)?i=r:rt(r)&&(s=!0,r=r[Fe]);let a=ot(r);e===0&&n!==null?o==null?im(t,n,a):Es(t,n,a,o||null,!0):e===1&&n!==null?Es(t,n,a,o||null,!0):e===2?Hu(t,a,s):e===3&&t.destroyNode(a),i!=null&&Tb(t,e,i,n,o)}}function vb(e,t){vm(e,t),t[Fe]=null,t[it]=null}function yb(e,t,n,r,o,i){r[Fe]=o,r[it]=t,Hs(e,r,n,1,o,i)}function vm(e,t){t[Yt].changeDetectionScheduler?.notify(9),Hs(e,t,t[Q],2,null,null)}function Db(e){let t=e[ro];if(!t)return il(e[M],e);for(;t;){let n=null;if(rt(t))n=t[ro];else{let r=t[Se];r&&(n=r)}if(!n){for(;t&&!t[nt]&&t!==e;)rt(t)&&il(t[M],t),t=t[le];t===null&&(t=e),rt(t)&&il(t[M],t),n=t&&t[nt]}t=n}}function Ju(e,t){let n=e[fr],r=n.indexOf(t);n.splice(r,1)}function Xu(e,t){if(Nn(t))return;let n=t[Q];n.destroyNode&&Hs(e,t,n,3,null,null),Db(t)}function il(e,t){if(Nn(t))return;let n=k(null);try{t[S]&=-129,t[S]|=256,t[He]&&Ec(t[He]),Eb(e,t),Cb(e,t),t[M].type===1&&t[Q].destroy();let r=t[Cn];if(r!==null&&yt(t[le])){r!==t[le]&&Ju(r,t);let o=t[Nt];o!==null&&o.detachView(e)}kl(t)}finally{k(n)}}function Cb(e,t){let n=e.cleanup,r=t[ls];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[ls]=null);let o=t[Wt];if(o!==null){t[Wt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[us];if(i!==null){t[us]=null;for(let s of i)s.destroy()}}function Eb(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof _n)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];$(4,a,c);try{c.call(a)}finally{$(5,a,c)}}else{$(4,o,i);try{i.call(o)}finally{$(5,o,i)}}}}}function wb(e,t,n){return _b(e,t.parent,n)}function _b(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Fe];if(An(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===vt.None||o===vt.Emulated)return null}return Dt(r,n)}function bb(e,t,n){return Mb(e,t,n)}function Ib(e,t,n){return e.type&40?Dt(e,n):null}var Mb=Ib,Hh;function ed(e,t,n,r){let o=wb(e,r,t),i=t[Q],s=r.parent||t[it],a=bb(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)$h(i,o,n[c],a,!1);else $h(i,o,n,a,!1);Hh!==void 0&&Hh(i,r,t,n,o)}function eo(e,t){if(t!==null){let n=t.type;if(n&3)return Dt(t,e);if(n&4)return zl(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return eo(e,r);{let o=e[t.index];return yt(o)?zl(-1,o):ot(o)}}else{if(n&128)return eo(e,t.next);if(n&32)return Ku(t,e)()||ot(e[t.index]);{let r=ym(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=En(e[ze]);return eo(o,r)}else return eo(e,t.next)}}}return null}function ym(e,t){if(t!==null){let r=e[ze][it],o=t.projection;return r.projection[o]}return null}function zl(e,t){let n=Se+e+1;if(n<t.length){let r=t[n],o=r[M].firstChild;if(o!==null)return eo(r,o)}return t[Rt]}function td(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&yo(ot(a),r),n.flags|=2),!Er(n))if(c&8)td(e,t,n.child,r,o,i,!1),ir(t,e,o,a,i);else if(c&32){let l=Ku(n,r),u;for(;u=l();)ir(t,e,o,u,i);ir(t,e,o,a,i)}else c&16?Sb(e,t,r,n,o,i):ir(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Hs(e,t,n,r,o,i){td(n,r,e.firstChild,t,o,i,!1)}function Sb(e,t,n,r,o,i){let s=n[ze],c=s[it].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];ir(t,e,o,u,i)}else{let l=c,u=s[le];Ds(r)&&(l.flags|=128),td(e,t,l,u,o,i,!0)}}function Tb(e,t,n,r,o){let i=n[Rt],s=ot(n);i!==s&&ir(t,e,r,i,o);for(let a=Se;a<n.length;a++){let c=n[a];Hs(c[M],c,e,t,r,i)}}function Ab(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Ot.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Ot.Important),e.setStyle(n,r,o,i))}}function ws(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(ot(i)),yt(i)&&Nb(i,r);let s=n.type;if(s&8)ws(e,t,n.child,r);else if(s&32){let a=Ku(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=ym(t,n);if(Array.isArray(a))r.push(...a);else{let c=En(t[ze]);ws(c[M],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Nb(e,t){for(let n=Se;n<e.length;n++){let r=e[n],o=r[M].firstChild;o!==null&&ws(r[M],r,o,t)}e[Rt]!==e[Fe]&&t.push(e[Rt])}function Dm(e){if(e[ar]!==null){for(let t of e[ar])t.impl.addSequence(t);e[ar].length=0}}var Cm=[];function xb(e){return e[He]??Rb(e)}function Rb(e){let t=Cm.pop()??Object.create(Pb);return t.lView=e,t}function Ob(e){e.lView[He]!==e&&(e.lView=null,Cm.push(e))}var Pb=O(m({},qr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{po(e.lView)},consumerOnSignalRead(){this.lView[He]=this}});function kb(e){let t=e[He]??Object.create(Fb);return t.lView=e,t}var Fb=O(m({},qr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=En(e.lView);for(;t&&!Em(t[M]);)t=En(t);t&&Wp(t)},consumerOnSignalRead(){this.lView[He]=this}});function Em(e){return e.type!==2}function wm(e){if(e[us]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[us])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[S]&8192)}}var Lb=100;function _m(e,t=!0,n=0){let o=e[Yt].rendererFactory,i=!1;i||o.begin?.();try{Vb(e,n)}catch(s){throw t&&Zu(e,s),s}finally{i||o.end?.()}}function Vb(e,t){let n=Jp();try{_h(!0),Gl(e,t);let r=0;for(;ks(e);){if(r===Lb)throw new D(103,!1);r++,Gl(e,1)}}finally{_h(n)}}function jb(e,t,n,r){if(Nn(t))return;let o=t[S],i=!1,s=!1;Su(t);let a=!0,c=null,l=null;i||(Em(e)?(l=xb(t),c=fi(l)):gc()===null?(a=!1,l=kb(t),c=fi(l)):t[He]&&(Ec(t[He]),t[He]=null));try{qp(t),ZE(e.bindingStartIndex),n!==null&&dm(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let d=e.preOrderCheckHooks;d!==null&&Ki(t,d,null)}else{let d=e.preOrderHooks;d!==null&&Ji(t,d,0,null),tl(t,0)}if(s||Bb(t),wm(t),bm(t,0),e.contentQueries!==null&&Xg(e,t),!i)if(u){let d=e.contentCheckHooks;d!==null&&Ki(t,d)}else{let d=e.contentHooks;d!==null&&Ji(t,d,1),tl(t,1)}$b(e,t);let f=e.components;f!==null&&Mm(t,f,0);let h=e.viewQuery;if(h!==null&&Bl(2,h,r),!i)if(u){let d=e.viewCheckHooks;d!==null&&Ki(t,d)}else{let d=e.viewHooks;d!==null&&Ji(t,d,2),tl(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[el]){for(let d of t[el])d();t[el]=null}i||(Dm(t),t[S]&=-73)}catch(u){throw i||po(t),u}finally{l!==null&&(Dc(l,c),a&&Ob(l)),Tu()}}function bm(e,t){for(let n=Rg(e);n!==null;n=Og(n))for(let r=Se;r<n.length;r++){let o=n[r];Im(o,t)}}function Bb(e){for(let t=Rg(e);t!==null;t=Og(t)){if(!(t[S]&2))continue;let n=t[fr];for(let r=0;r<n.length;r++){let o=n[r];Wp(o)}}}function Ub(e,t,n){$(18);let r=gt(t,e);Im(r,n),$(19,r[Te])}function Im(e,t){wu(e)&&Gl(e,t)}function Gl(e,t){let r=e[M],o=e[S],i=e[He],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Cc(i)),s||=!1,i&&(i.dirty=!1),e[S]&=-9217,s)jb(r,e,r.template,e[Te]);else if(o&8192){wm(e),bm(e,1);let a=r.components;a!==null&&Mm(e,a,1),Dm(e)}}function Mm(e,t,n){for(let r=0;r<t.length;r++)Ub(e,t[r],n)}function $b(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)wn(~o);else{let i=o,s=n[++r],a=n[++r];QE(s,i);let c=t[i];$(24,c),a(2,c),$(25,c)}}}finally{wn(-1)}}function zs(e,t){let n=Jp()?64:1088;for(e[Yt].changeDetectionScheduler?.notify(t);e;){e[S]|=n;let r=En(e);if(oo(e)&&!r)return e;e=r}return null}function Sm(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Tm(e,t,n,r=!0){let o=t[M];if(zb(o,t,e,n),r){let s=zl(n,e),a=t[Q],c=a.parentNode(e[Rt]);c!==null&&yb(o,e[it],a,t,c,s)}let i=t[ft];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Hb(e,t){let n=_s(e,t);return n!==void 0&&Xu(n[M],n),n}function _s(e,t){if(e.length<=Se)return;let n=Se+t,r=e[n];if(r){let o=r[Cn];o!==null&&o!==e&&Ju(o,r),t>0&&(e[n-1][nt]=r[nt]);let i=as(e,Se+t);vb(r[M],r);let s=i[Nt];s!==null&&s.detachView(i[M]),r[le]=null,r[nt]=null,r[S]&=-129}return r}function zb(e,t,n,r){let o=Se+r,i=n.length;r>0&&(n[o-1][nt]=t),r<i-Se?(t[nt]=n[o],Sp(n,Se+r,t)):(n.push(t),t[nt]=null),t[le]=n;let s=t[Cn];s!==null&&n!==s&&Am(s,t);let a=t[Nt];a!==null&&a.insertView(e),bl(t),t[S]|=128}function Am(e,t){let n=e[fr],r=t[le];if(rt(r))e[S]|=2;else{let o=r[le][ze];t[ze]!==o&&(e[S]|=2)}n===null?e[fr]=[t]:n.push(t)}var ao=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[M];return ws(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[Te]}set context(t){this._lView[Te]=t}get destroyed(){return Nn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[le];if(yt(t)){let n=t[ds],r=n?n.indexOf(this):-1;r>-1&&(_s(t,r),as(n,r))}this._attachedToViewContainer=!1}Xu(this._lView[M],this._lView)}onDestroy(t){Zp(this._lView,t)}markForCheck(){zs(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[S]&=-129}reattach(){bl(this._lView),this._lView[S]|=128}detectChanges(){this._lView[S]|=1024,_m(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new D(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=oo(this._lView),n=this._lView[Cn];n!==null&&!t&&Ju(n,this._lView),vm(this._lView[M],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new D(902,!1);this._appRef=t;let n=oo(this._lView),r=this._lView[Cn];r!==null&&!n&&Am(r,this._lView),bl(this._lView)}};var In=(()=>{class e{static __NG_ELEMENT_ID__=Wb}return e})(),Gb=In,qb=class extends Gb{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=mm(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new ao(o)}};function Wb(){return nd(Ve(),H())}function nd(e,t){return e.type&4?new qb(t,e,yr(e,t)):null}function rd(e,t,n,r,o){let i=e.data[t];if(i===null)i=Zb(e,t,n,r,o),YE()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=GE();i.injectorIndex=s===null?-1:s.injectorIndex}return mo(i,!0),i}function Zb(e,t,n,r,o){let i=Qp(),s=Kp(),a=s?i:i&&i.parent,c=e.data[t]=Qb(e,a,n,t,r,o);return Yb(e,c,i,s),c}function Yb(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Qb(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return go()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var Kb=new RegExp(`^(\\d+)*(${Lg}|${Fg})*(.*)`);function Jb(e){let t=e.match(Kb),[n,r,o,i]=t,s=r?parseInt(r,10):o,a=[];for(let[c,l,u]of i.matchAll(/(f|n)(\d*)/g)){let f=parseInt(u,10)||1;a.push(l,f)}return[s,...a]}function Xb(e){return!e.prev&&e.parent?.type===8}function sl(e){return e.index-ue}function eI(e,t){let n=e.i18nNodes;if(n)return n.get(t)}function Gs(e,t,n,r){let o=sl(r),i=eI(e,o);if(i===void 0){let s=e.data[qw];if(s?.[o])i=nI(s[o],n);else if(t.firstChild===r)i=e.firstChild;else{let a=r.prev===null,c=r.prev??r.parent;if(Xb(r)){let l=sl(r.parent);i=jl(e,l)}else{let l=Dt(c,n);if(a)i=l.firstChild;else{let u=sl(c),f=jl(e,u);if(c.type===2&&f){let d=ju(e,u)+1;i=qs(d,f)}else i=l.nextSibling}}}}return i}function qs(e,t){let n=t;for(let r=0;r<e;r++)n=n.nextSibling;return n}function tI(e,t){let n=e;for(let r=0;r<t.length;r+=2){let o=t[r],i=t[r+1];for(let s=0;s<i;s++)switch(o){case Uw:n=n.firstChild;break;case $w:n=n.nextSibling;break}}return n}function nI(e,t){let[n,...r]=Jb(e),o;if(n===Fg)o=t[ze][Fe];else if(n===Lg)o=j_(t[ze][Fe]);else{let i=Number(n);o=ot(t[i+ue])}return tI(o,r)}var rI=!1;function oI(e){rI=e}function iI(e){let t=e[ft];if(t){let{i18nNodes:n,dehydratedIcuData:r}=t;if(n&&r){let o=e[Q];for(let i of r.values())sI(o,n,i)}t.i18nNodes=void 0,t.dehydratedIcuData=void 0}}function sI(e,t,n){for(let r of n.node.cases[n.case]){let o=t.get(r.index-ue);o&&Hu(e,o,!1)}}function Nm(e){let t=e[xt]??[],r=e[le][Q],o=[];for(let i of t)i.data[Ww]!==void 0?o.push(i):xm(i,r);e[xt]=o}function aI(e){let{lContainer:t}=e,n=t[xt];if(n===null)return;let o=t[le][Q];for(let i of n)xm(i,o)}function xm(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[Cs];for(;n<o;){let i=r.nextSibling;Hu(t,r,!1),r=i,n++}}}function Ws(e){Nm(e);let t=e[Fe];rt(t)&&bs(t);for(let n=Se;n<e.length;n++)bs(e[n])}function bs(e){iI(e);let t=e[M];for(let n=ue;n<t.bindingStartIndex;n++)if(yt(e[n])){let r=e[n];Ws(r)}else rt(e[n])&&bs(e[n])}function Rm(e){let t=e._views;for(let n of t){let r=S_(n);r!==null&&r[Fe]!==null&&(rt(r)?bs(r):Ws(r))}}function cI(e,t,n,r){e!==null&&(n.cleanup(t),Ws(e.lContainer),Rm(r))}function lI(e,t){let n=[];for(let r of t)for(let o=0;o<(r[Vg]??1);o++){let i={data:r,firstChild:null};r[Cs]>0&&(i.firstChild=e,e=qs(r[Cs],e)),n.push(i)}return[e,n]}var Om=()=>null;function uI(e,t){let n=e[xt];return!t||n===null||n.length===0?null:n[0].data[Gw]===t?n.shift():(Nm(e),null)}function dI(){Om=uI}function zh(e,t){return Om(e,t)}var fI=class{},Pm=class{},ql=class{resolveComponentFactory(t){throw Error(`No component factory found for ${Me(t)}.`)}},Zs=class{static NULL=new ql},hr=class{},wr=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>hI()}return e})();function hI(){let e=H(),t=Ve(),n=gt(t.index,e);return(rt(n)?n:e)[Q]}var pI=(()=>{class e{static \u0275prov=C({token:e,providedIn:"root",factory:()=>null})}return e})();var al={},ur=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=xs(r);let o=this.injector.get(t,al,r);return o!==al||n===al?o:this.parentInjector.get(t,n,r)}};function Gh(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=ml(o,a);else if(i==2){let c=a,l=t[++s];r=ml(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function B(e,t=x.Default){let n=H();if(n===null)return w(e,t);let r=Ve();return vg(r,n,ve(e),t)}function km(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,l=null,u=mI(s);u===null?a=s:[a,c,l]=u,DI(e,t,n,a,i,c,l)}i!==null&&r!==null&&gI(n,r,i)}function gI(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new D(-301,!1);r.push(t[o],i)}}function mI(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&pt(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,vI(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function vI(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function yI(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function DI(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let d=r[h];!c&&pt(d)&&(c=!0,yI(e,n,h)),Tl(ms(n,t),e,d.type)}II(n,e.data.length,a);for(let h=0;h<a;h++){let d=r[h];d.providersResolver&&d.providersResolver(d)}let l=!1,u=!1,f=lm(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let h=0;h<a;h++){let d=r[h];if(n.mergedAttrs=io(n.mergedAttrs,d.hostAttrs),EI(e,n,t,f,d),bI(f,d,o),s!==null&&s.has(d)){let[v,E]=s.get(d);n.directiveToIndex.set(d.type,[f,v+n.directiveStart,E+n.directiveStart])}else(i===null||!i.has(d))&&n.directiveToIndex.set(d.type,f);d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let p=d.type.prototype;!l&&(p.ngOnChanges||p.ngOnInit||p.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(p.ngOnChanges||p.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),f++}CI(e,n,i)}function CI(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))qh(0,t,o,r),qh(1,t,o,r),Zh(t,r,!1);else{let i=n.get(o);Wh(0,t,i,r),Wh(1,t,i,r),Zh(t,r,!0)}}}function qh(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Fm(t,i)}}function Wh(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Fm(t,s)}}function Fm(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Zh(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Uu(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function EI(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=vn(o.type,!0)),s=new _n(i,pt(o),B);e.blueprint[r]=s,n[r]=s,wI(e,t,r,lm(e,n,o.hostVars,Pt),o)}function wI(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;_I(s)!=a&&s.push(a),s.push(n,r,i)}}function _I(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function bI(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;pt(t)&&(n[""]=e)}}function II(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Lm(e,t,n,r,o,i,s,a){let c=t.consts,l=fs(c,s),u=rd(t,e,2,r,l);return i&&km(t,n,u,fs(c,a),o),u.mergedAttrs=io(u.mergedAttrs,u.attrs),u.attrs!==null&&Gh(u,u.attrs,!1),u.mergedAttrs!==null&&Gh(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function Vm(e,t){lg(e,t),Bp(t)&&e.queries.elementEnd(t)}var Is=class extends Zs{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Dn(t);return new co(n,this.ngModule)}};function MI(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&$s.SignalBased)!==0};return o&&(i.transform=o),i})}function SI(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function TI(e,t,n){let r=t instanceof ye?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new ur(n,r):n}function AI(e){let t=e.get(hr,null);if(t===null)throw new D(407,!1);let n=e.get(pI,null),r=e.get(bn,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function NI(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return $u(t,n,n==="svg"?NE:n==="math"?xE:null)}var co=class extends Pm{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=MI(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=SI(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Y_(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){$(22);let i=k(null);try{let s=this.componentDef,a=r?["ng-version","19.2.14"]:Q_(this.componentDef.selectors[0]),c=zu(0,null,null,1,0,null,null,null,null,[a],null),l=TI(s,o||this.ngModule,t),u=AI(l),f=u.rendererFactory.createRenderer(null,s),h=r?rb(f,r,s.encapsulation,l):NI(s,f),d=Gu(null,c,null,512|cm(s),null,null,u,f,l,null,Kg(h,l,!0));d[ue]=h,Su(d);let p=null;try{let v=Lm(ue,c,d,"#host",()=>[this.componentDef],!0,0);h&&(am(f,h,v),yo(h,d)),Wu(c,d,v),em(c,v,d),Vm(c,v),n!==void 0&&xI(v,this.ngContentSelectors,n),p=gt(v.index,d),d[Te]=p[Te],Qu(c,d,null)}catch(v){throw p!==null&&kl(p),kl(d),v}finally{$(23),Tu()}return new Wl(this.componentType,d)}finally{k(i)}}},Wl=class extends fI{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=Ps(n[M],ue),this.location=yr(this._tNode,n),this.instance=gt(this._tNode.index,n)[Te],this.hostView=this.changeDetectorRef=new ao(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Yu(r,o[M],o,t,n);this.previousInputValues.set(t,n);let s=gt(r.index,o);zs(s,1)}get injector(){return new mn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function xI(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var en=(()=>{class e{static __NG_ELEMENT_ID__=RI}return e})();function RI(){let e=Ve();return Bm(e,H())}var OI=en,jm=class extends OI{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return yr(this._hostTNode,this._hostLView)}get injector(){return new mn(this._hostTNode,this._hostLView)}get parentInjector(){let t=Nu(this._hostTNode,this._hostLView);if(dg(t)){let n=ps(t,this._hostLView),r=hs(t),o=n[M].data[r+8];return new mn(o,n)}else return new mn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Yh(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Se}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=zh(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Hl(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!ME(t),a;if(s)a=n;else{let p=n||{};a=p.index,r=p.injector,o=p.projectableNodes,i=p.environmentInjector||p.ngModuleRef}let c=s?t:new co(Dn(t)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let v=(s?l:this.parentInjector).get(ye,null);v&&(i=v)}let u=Dn(c.componentType??{}),f=zh(this._lContainer,u?.id??null),h=f?.firstChild??null,d=c.create(l,o,h,i);return this.insertImpl(d.hostView,a,Hl(this._hostTNode,f)),d}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(kE(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[le],l=new jm(c,c[it],c[le]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Tm(s,o,i,r),t.attachToViewContainerRef(),Sp(cl(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Yh(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=_s(this._lContainer,n);r&&(as(cl(this._lContainer),n),Xu(r[M],r))}detach(t){let n=this._adjustIndex(t,-1),r=_s(this._lContainer,n);return r&&as(cl(this._lContainer),n)!=null?new ao(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Yh(e){return e[ds]}function cl(e){return e[ds]||(e[ds]=[])}function Bm(e,t){let n,r=t[e.index];return yt(r)?n=r:(n=Sm(r,t,null,e),t[e.index]=n,qu(t,n)),Um(n,t,e,r),new jm(n,e,t)}function PI(e,t){let n=e[Q],r=n.createComment(""),o=Dt(t,e),i=n.parentNode(o);return Es(n,i,r,n.nextSibling(o),!1),r}var Um=$m,od=()=>!1;function kI(e,t,n){return od(e,t,n)}function $m(e,t,n,r){if(e[Rt])return;let o;n.type&8?o=ot(r):o=PI(t,n),e[Rt]=o}function FI(e,t,n){if(e[Rt]&&e[xt])return!0;let r=n[ft],o=t.index-ue;if(!r||Pw(t)||Co(r,o))return!1;let s=jl(r,o),a=r.data[ku]?.[o],[c,l]=lI(s,a);return e[Rt]=c,e[xt]=l,!0}function LI(e,t,n,r){od(e,n,t)||$m(e,t,n,r)}function VI(){Um=LI,od=FI}var Zl=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Yl=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)id(t,n).matches!==null&&this.queries[n].setDirty()}},Ql=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=qI(t):this.predicate=t}},Kl=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Jl=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,jI(n,i)),this.matchTNodeWithReadOption(t,n,Xi(n,t,i,!1,!1))}else r===In?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Xi(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Ct||o===en||o===In&&n.type&4)this.addMatch(n.index,-2);else{let i=Xi(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function jI(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function BI(e,t){return e.type&11?yr(e,t):e.type&4?nd(e,t):null}function UI(e,t,n,r){return n===-1?BI(t,e):n===-2?$I(e,t,r):so(e,e[M],n,t)}function $I(e,t,n){if(n===Ct)return yr(t,e);if(n===In)return nd(t,e);if(n===en)return Bm(t,e)}function Hm(e,t,n,r){let o=t[Nt].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(UI(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Xl(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Hm(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let f=Se;f<u.length;f++){let h=u[f];h[Cn]===h[le]&&Xl(h[M],h,l,r)}if(u[fr]!==null){let f=u[fr];for(let h=0;h<f.length;h++){let d=f[h];Xl(d[M],d,l,r)}}}}}return r}function HI(e,t){return e[Nt].queries[t].queryList}function zI(e,t,n){let r=new Pl((n&4)===4);return VE(e,t,r,r.destroy),(t[Nt]??=new Yl).queries.push(new Zl(r))-1}function GI(e,t,n){let r=ge();return r.firstCreatePass&&(WI(r,new Ql(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),zI(r,H(),t)}function qI(e){return e.split(",").map(t=>t.trim())}function WI(e,t,n){e.queries===null&&(e.queries=new Kl),e.queries.track(new Jl(t,n))}function id(e,t){return e.queries.getByIndex(t)}function ZI(e,t){let n=e[M],r=id(n,t);return r.crossesNgTemplate?Xl(n,e,t,[]):Hm(n,e,r,t)}var pr=class{},sd=class{};var eu=class extends pr{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Is(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Np(t);this._bootstrapComponents=tm(i.bootstrap),this._r3Injector=Cg(t,n,[{provide:pr,useValue:this},{provide:Zs,useValue:this.componentFactoryResolver},...r],Me(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},tu=class extends sd{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new eu(this.moduleType,t,[])}};var Ms=class extends pr{injector;componentFactoryResolver=new Is(this);instance=null;constructor(t){super();let n=new no([...t.providers,{provide:pr,useValue:this},{provide:Zs,useValue:this.componentFactoryResolver}],t.parent||Cu(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Eo(e,t,n=null){return new Ms({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var YI=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=yu(!1,n.type),o=r.length>0?Eo([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=C({token:e,providedIn:"environment",factory:()=>new e(w(ye))})}return e})();function _r(e){return uo(()=>{let t=Gm(e),n=O(m({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Ng.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(YI).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||vt.Emulated,styles:e.styles||Ie,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Xt("NgStandalone"),qm(n);let r=e.dependencies;return n.directiveDefs=Qh(r,!1),n.pipeDefs=Qh(r,!0),n.id=eM(n),n})}function QI(e){return Dn(e)||xp(e)}function KI(e){return e!==null}function tn(e){return uo(()=>({type:e.type,bootstrap:e.bootstrap||Ie,declarations:e.declarations||Ie,imports:e.imports||Ie,exports:e.exports||Ie,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function JI(e,t){if(e==null)return yn;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=$s.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function XI(e){if(e==null)return yn;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function De(e){return uo(()=>{let t=Gm(e);return qm(t),t})}function zm(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Gm(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||yn,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Ie,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:JI(e.inputs,t),outputs:XI(e.outputs),debugInfo:null}}function qm(e){e.features?.forEach(t=>t(e))}function Qh(e,t){if(!e)return null;let n=t?Rp:QI;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(KI)}function eM(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function tM(e){return Object.getPrototypeOf(e.prototype).constructor}function nn(e){let t=tM(e.type),n=!0,r=[e];for(;t;){let o;if(pt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new D(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=ll(e.inputs),s.declaredInputs=ll(e.declaredInputs),s.outputs=ll(e.outputs);let a=o.hostBindings;a&&sM(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&oM(e,c),l&&iM(e,l),nM(e,o),ZC(e.outputs,o.outputs),pt(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===nn&&(n=!1)}}t=Object.getPrototypeOf(t)}rM(r)}function nM(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function rM(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=io(o.hostAttrs,n=io(n,o.hostAttrs))}}function ll(e){return e===yn?{}:e===Ie?[]:e}function oM(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function iM(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function sM(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function Wm(e){return cM(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function aM(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function cM(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function lM(e,t,n){return e[t]=n}function Mn(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function uM(e,t,n,r){let o=Mn(e,t,n);return Mn(e,t+1,r)||o}function dM(e,t,n,r,o,i,s,a,c){let l=t.consts,u=rd(t,e,4,s||null,a||null);Yp()&&km(t,n,u,fs(l,c),gm),u.mergedAttrs=io(u.mergedAttrs,u.attrs),lg(t,u);let f=u.tView=zu(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),f.queries=t.queries.embeddedTView(u)),u}function fM(e,t,n,r,o,i,s,a,c,l){let u=n+ue,f=t.firstCreatePass?dM(u,t,e,r,o,i,s,a,c):t.data[u];mo(f,!1);let h=Zm(t,e,f,n);Au()&&ed(t,e,h,f),yo(h,e);let d=Sm(h,e,h,f);return e[u]=d,qu(e,d),kI(d,f,e),Eu(f)&&Wu(t,e,f),c!=null&&fm(e,f,l),f}function br(e,t,n,r,o,i,s,a){let c=H(),l=ge(),u=fs(l.consts,i);return fM(c,l,e,t,n,r,o,u,s,a),br}var Zm=Ym;function Ym(e,t,n,r){return Jt(!0),t[Q].createComment("")}function hM(e,t,n,r){let o=t[ft],i=!o||go()||Er(n)||Co(o,r);if(Jt(i),i)return Ym(e,t);let s=o.data[zw]?.[r]??null;s!==null&&n.tView!==null&&n.tView.ssrId===null&&(n.tView.ssrId=s);let a=Gs(o,e,t,n);Us(o,r,a);let c=ju(o,r);return qs(c,a)}function pM(){Zm=hM}var gM=(()=>{class e{cachedInjectors=new Map;getOrCreateInjector(n,r,o,i){if(!this.cachedInjectors.has(n)){let s=o.length>0?Eo(o,r,i):null;this.cachedInjectors.set(n,s)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=C({token:e,providedIn:"environment",factory:()=>new e})}return e})();var mM=new y("");function ul(e,t,n){return e.get(gM).getOrCreateInjector(t,e,n,"")}function vM(e,t,n){if(e instanceof ur){let o=e.injector,i=e.parentInjector,s=ul(i,t,n);return new ur(o,s)}let r=e.get(ye);if(r!==e){let o=ul(r,t,n);return new ur(e,o)}return ul(e,t,n)}function sr(e,t,n,r=!1){let o=n[le],i=o[M];if(Nn(o))return;let s=Do(o,t),a=s[Vs],c=s[a_];if(!(c!==null&&e<c)&&Kh(a,e)&&Kh(s[o_]??-1,e)){let l=js(i,t),f=!r&&!0&&(f_(l)!==null||Lh(l,ce.Loading)!==null||Lh(l,ce.Placeholder))?CM:DM;try{f(e,s,n,t,o)}catch(h){Zu(o,h)}}}function yM(e,t){let n=e[xt]?.findIndex(o=>o.data[Zw]===t[Vs])??-1;return{dehydratedView:n>-1?e[xt][n]:null,dehydratedViewIx:n}}function DM(e,t,n,r,o){$(20);let i=d_(e,o,r);if(i!==null){t[Vs]=e;let s=o[M],a=i+ue,c=Ps(s,a),l=0;Hb(n,l);let u;if(e===ce.Complete){let p=js(s,r),v=p.providers;v&&v.length>0&&(u=vM(o[ht],p,v))}let{dehydratedView:f,dehydratedViewIx:h}=yM(n,t),d=mm(o,c,null,{injector:u,dehydratedView:f});if(Tm(n,d,l,Hl(c,f)),zs(d,2),h>-1&&n[xt]?.splice(h,1),(e===ce.Complete||e===ce.Error)&&Array.isArray(t[lr])){for(let p of t[lr])p();t[lr]=null}}$(21)}function Kh(e,t){return e<t}function Jh(e,t,n){e.loadingPromise.then(()=>{e.loadingState===ke.COMPLETE?sr(ce.Complete,t,n):e.loadingState===ke.FAILED&&sr(ce.Error,t,n)})}var CM=null;var ad=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Qm=new y("");var EM=(()=>{class e{static \u0275prov=C({token:e,providedIn:"root",factory:()=>new nu})}return e})(),nu=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function On(e){return!!e&&typeof e.then=="function"}function Km(e){return!!e&&typeof e.subscribe=="function"}var wM=new y("");var Jm=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=g(wM,{optional:!0})??[];injector=g(Ge);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=Le(this.injector,o);if(On(i))n.push(i);else if(Km(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Pn=new y("");function _M(){_c(()=>{throw new D(600,!1)})}function bM(e){return e.isBoundToModule}var IM=10;var Ae=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=g(Iw);afterRenderManager=g(zg);zonelessEnabled=g(Fs);rootEffectScheduler=g(EM);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new W;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=g(st).hasPendingTasks.pipe(I(n=>!n));constructor(){g(Cr,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=g(ye);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=Ge.NULL){$(10);let i=n instanceof Pm;if(!this._injector.get(Jm).done){let d="";throw new D(405,d)}let a;i?a=n:a=this._injector.get(Zs).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=bM(a)?void 0:this._injector.get(pr),l=r||a.selector,u=a.create(o,[],l,c),f=u.location.nativeElement,h=u.injector.get(Qm,null);return h?.registerApplication(f),u.onDestroy(()=>{this.detachView(u.hostView),ts(this.components,u),h?.unregisterApplication(f)}),this._loadComponent(u),$(11,u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){$(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Fu.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new D(101,!1);let n=k(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,k(n),this.afterTick.next(),$(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(hr,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<IM;)$(14),this.synchronizeOnce(),$(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)MM(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>ks(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;ts(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Pn,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>ts(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new D(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ts(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function MM(e,t,n,r){if(!n&&!ks(e))return;_m(e,t,n&&!r?0:1)}function SM(e,t,n){let r=t[ht],o=t[M];if(e.loadingState!==ke.NOT_STARTED)return e.loadingPromise??Promise.resolve();let i=Do(t,n),s=h_(o,e);e.loadingState=ke.IN_PROGRESS,es(1,i);let a=e.dependencyResolverFn,c=r.get(yw).add();return a?(e.loadingPromise=Promise.allSettled(a()).then(l=>{let u=!1,f=[],h=[];for(let d of l)if(d.status==="fulfilled"){let p=d.value,v=Dn(p)||xp(p);if(v)f.push(v);else{let E=Rp(p);E&&h.push(E)}}else{u=!0;break}if(u){if(e.loadingState=ke.FAILED,e.errorTmplIndex===null){let d="",p=new D(-750,!1);Zu(t,p)}}else{e.loadingState=ke.COMPLETE;let d=s.tView;if(f.length>0){d.directiveRegistry=Vh(d.directiveRegistry,f);let p=f.map(E=>E.type),v=yu(!1,...p);e.providers=v}h.length>0&&(d.pipeRegistry=Vh(d.pipeRegistry,h))}}),e.loadingPromise.finally(()=>{e.loadingPromise=null,c()})):(e.loadingPromise=Promise.resolve().then(()=>{e.loadingPromise=null,e.loadingState=ke.COMPLETE,c()}),e.loadingPromise)}function TM(e,t){return t[ht].get(mM,null,{optional:!0})?.behavior!==Gg.Manual}function AM(e,t,n){let r=t[M],o=t[n.index];if(!TM(e,t))return;let i=Do(t,n),s=js(r,n);switch(l_(i),s.loadingState){case ke.NOT_STARTED:sr(ce.Loading,n,o),SM(s,t,n),s.loadingState===ke.IN_PROGRESS&&Jh(s,n,o);break;case ke.IN_PROGRESS:sr(ce.Loading,n,o),Jh(s,n,o);break;case ke.COMPLETE:sr(ce.Complete,n,o);break;case ke.FAILED:sr(ce.Error,n,o);break;default:}}function NM(e,t,n){return Ut(this,null,function*(){let r=e.get(Vu);if(r.hydrating.has(t))return;let{parentBlockPromise:i,hydrationQueue:s}=O_(t,e);if(s.length===0)return;i!==null&&s.shift(),OM(r,s),i!==null&&(yield i);let a=s[0];r.has(a)?yield Xh(e,s,n):r.awaitParentBlock(a,()=>Ut(null,null,function*(){return yield Xh(e,s,n)}))})}function Xh(e,t,n){return Ut(this,null,function*(){let r=e.get(Vu),o=r.hydrating,i=e.get(st),s=i.add();for(let c=0;c<t.length;c++){let l=t[c],u=r.get(l);if(u!=null){if(yield kM(u),yield PM(e),xM(u)){aI(u),ep(t.slice(c),r);break}o.get(l).resolve()}else{RM(c,t,r),ep(t.slice(c),r);break}}let a=t[t.length-1];yield o.get(a)?.promise,i.remove(s),n&&n(t),cI(r.get(a),t,r,e.get(Ae))})}function xM(e){return Do(e.lView,e.tNode)[Vs]===ce.Error}function RM(e,t,n){let r=e-1,o=r>-1?n.get(t[r]):null;o&&Ws(o.lContainer)}function ep(e,t){let n=t.hydrating;for(let r in e)n.get(r)?.reject();t.cleanup(e)}function OM(e,t){for(let n of t)e.hydrating.set(n,Promise.withResolvers())}function PM(e){return new Promise(t=>Lu(t,{injector:e}))}function kM(e){return Ut(this,null,function*(){let{tNode:t,lView:n}=e,r=Do(n,t);return new Promise(o=>{FM(r,o),AM(2,n,t)})})}function FM(e,t){Array.isArray(e[lr])||(e[lr]=[]),e[lr].push(t)}function LM(e,t,n,r){return Mn(e,Iu(),n)?t+Ep(n)+r:Pt}function Zi(e,t){return e<<17|t<<2}function Sn(e){return e>>17&32767}function VM(e){return(e&2)==2}function jM(e,t){return e&131071|t<<17}function ru(e){return e|2}function gr(e){return(e&131068)>>2}function dl(e,t){return e&-131069|t<<2}function BM(e){return(e&1)===1}function ou(e){return e|1}function UM(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Sn(s),c=gr(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let f=n;u=f[1],(u===null||fo(f,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let h=Sn(e[a+1]);e[r+1]=Zi(h,a),h!==0&&(e[h+1]=dl(e[h+1],r)),e[a+1]=jM(e[a+1],r)}else e[r+1]=Zi(a,0),a!==0&&(e[a+1]=dl(e[a+1],r)),a=r;else e[r+1]=Zi(c,0),a===0?a=r:e[c+1]=dl(e[c+1],r),c=r;l&&(e[r+1]=ru(e[r+1])),tp(e,u,r,!0),tp(e,u,r,!1),$M(t,u,e,r,i),s=Zi(a,c),i?t.classBindings=s:t.styleBindings=s}function $M(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&fo(i,t)>=0&&(n[r+1]=ou(n[r+1]))}function tp(e,t,n,r){let o=e[n+1],i=t===null,s=r?Sn(o):gr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];HM(c,t)&&(a=!0,e[s+1]=r?ou(l):ru(l)),s=r?Sn(l):gr(l)}a&&(e[n+1]=r?ru(o):ou(o))}function HM(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?fo(e,t)>=0:!1}var tt={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function zM(e){return e.substring(tt.key,tt.keyEnd)}function GM(e){return qM(e),Xm(e,ev(e,0,tt.textEnd))}function Xm(e,t){let n=tt.textEnd;return n===t?-1:(t=tt.keyEnd=WM(e,tt.key=t,n),ev(e,t,n))}function qM(e){tt.key=0,tt.keyEnd=0,tt.value=0,tt.valueEnd=0,tt.textEnd=e.length}function ev(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function WM(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function Ze(e,t,n){let r=H(),o=Iu();if(Mn(r,o,t)){let i=ge(),s=sg();pm(i,s,r,e,t,r[Q],n,!1)}return Ze}function iu(e,t,n,r,o){Yu(t,e,n,o?"class":"style",r)}function kt(e,t){return YM(e,t,null,!0),kt}function tv(e){QM(nS,ZM,e,!0)}function ZM(e,t){for(let n=GM(t);n>=0;n=Xm(t,n))vu(e,zM(t),!0)}function YM(e,t,n,r){let o=H(),i=ge(),s=Xp(2);if(i.firstUpdatePass&&rv(i,e,s,r),t!==Pt&&Mn(o,s,t)){let a=i.data[xn()];ov(i,a,o,o[Q],e,o[s+1]=oS(t,n),r,s)}}function QM(e,t,n,r){let o=ge(),i=Xp(2);o.firstUpdatePass&&rv(o,null,i,r);let s=H();if(n!==Pt&&Mn(s,i,n)){let a=o.data[xn()];if(iv(a,r)&&!nv(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=ml(c,n||"")),iu(o,a,s,n,r)}else rS(o,a,s,s[Q],s[i+1],s[i+1]=tS(e,t,n),r,i)}}function nv(e,t){return t>=e.expandoStartIndex}function rv(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[xn()],s=nv(e,n);iv(i,r)&&t===null&&!s&&(t=!1),t=KM(o,i,t,r),UM(o,i,t,n,s,r)}}function KM(e,t,n,r){let o=JE(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=fl(null,e,t,n,r),n=lo(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=fl(o,e,t,n,r),i===null){let c=JM(e,t,r);c!==void 0&&Array.isArray(c)&&(c=fl(null,e,t,c[1],r),c=lo(c,t.attrs,r),XM(e,t,r,c))}else i=eS(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function JM(e,t,n){let r=n?t.classBindings:t.styleBindings;if(gr(r)!==0)return e[Sn(r)]}function XM(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Sn(o)]=r}function eS(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=lo(r,s,n)}return lo(r,t.attrs,n)}function fl(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=lo(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function lo(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),vu(e,s,n?!0:t[++i]))}return e===void 0?null:e}function tS(e,t,n){if(n==null||n==="")return Ie;let r=[],o=Bu(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function nS(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&vu(e,r,n)}function rS(e,t,n,r,o,i,s,a){o===Pt&&(o=Ie);let c=0,l=0,u=0<o.length?o[0]:null,f=0<i.length?i[0]:null;for(;u!==null||f!==null;){let h=c<o.length?o[c+1]:void 0,d=l<i.length?i[l+1]:void 0,p=null,v;u===f?(c+=2,l+=2,h!==d&&(p=f,v=d)):f===null||u!==null&&u<f?(c+=2,p=u):(l+=2,p=f,v=d),p!==null&&ov(e,t,n,r,p,v,s,a),u=c<o.length?o[c]:null,f=l<i.length?i[l]:null}}function ov(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=BM(l)?np(c,t,n,o,gr(l),s):void 0;if(!Ss(u)){Ss(i)||VM(l)&&(i=np(c,null,n,o,a,s));let f=Gp(xn(),n);Ab(r,s,f,o,i)}}function np(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,f=u===null,h=n[o+1];h===Pt&&(h=f?Ie:void 0);let d=f?Jc(h,r):u===r?h:void 0;if(l&&!Ss(d)&&(d=Jc(c,r)),Ss(d)&&(a=d,s))return a;let p=e[o+1];o=s?Sn(p):gr(p)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Jc(c,r))}return a}function Ss(e){return e!==void 0}function oS(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Me(Bu(e)))),e}function iv(e,t){return(e.flags&(t?8:16))!==0}function A(e,t,n,r){let o=H(),i=ge(),s=ue+e,a=o[Q],c=i.firstCreatePass?Lm(s,i,o,t,gm,Yp(),n,r):i.data[s],l=sv(i,o,c,a,t,e);o[s]=l;let u=Eu(c);return mo(c,!0),am(a,l,c),!Er(c)&&Au()&&ed(i,o,l,c),(jE()===0||u)&&yo(l,o),BE(),u&&(Wu(i,o,c),em(i,c,o)),r!==null&&fm(o,c),A}function P(){let e=Ve();Kp()?qE():(e=e.parent,mo(e,!1));let t=e;$E(t)&&zE(),UE();let n=ge();return n.firstCreatePass&&Vm(n,t),t.classesWithoutHost!=null&&rw(t)&&iu(n,t,H(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&ow(t)&&iu(n,t,H(),t.stylesWithoutHost,!1),P}function fe(e,t,n,r){return A(e,t,n,r),P(),fe}var sv=(e,t,n,r,o,i)=>(Jt(!0),$u(r,o,ag()));function iS(e,t,n,r,o,i){let s=t[ft],a=!s||go()||Er(n)||Co(s,i);if(Jt(a),a)return $u(r,o,ag());let c=Gs(s,e,t,n);return Jg(s,i)&&Us(s,i,c.nextSibling),s&&(Tg(n)||Ag(c))&&An(n)&&(HE(n),sm(c)),c}function sS(){sv=iS}var aS=(e,t,n,r)=>(Jt(!0),om(t[Q],""));function cS(e,t,n,r){let o,i=t[ft],s=!i||go()||Co(i,r)||Er(n);if(Jt(s),s)return om(t[Q],"");let a=Gs(i,e,t,n),c=x_(i,r);return Us(i,r,a),o=qs(c,a),o}function lS(){aS=cS}function Ys(){return H()}var pn=void 0;function uS(e){let t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&n===0?1:5}var dS=["en",[["a","p"],["AM","PM"],pn],[["AM","PM"],pn,pn],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],pn,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],pn,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",pn,"{1} 'at' {0}",pn],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",uS],hl={};function Ye(e){let t=fS(e),n=rp(t);if(n)return n;let r=t.split("-")[0];if(n=rp(r),n)return n;if(r==="en")return dS;throw new D(701,!1)}function rp(e){return e in hl||(hl[e]=tr.ng&&tr.ng.common&&tr.ng.common.locales&&tr.ng.common.locales[e]),hl[e]}var te=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(te||{});function fS(e){return e.toLowerCase().replace(/_/g,"-")}var Ts="en-US";var hS=Ts;function pS(e){typeof e=="string"&&(hS=e.toLowerCase().replace(/_/g,"-"))}function op(e,t,n){return function r(o){if(o===Function)return n;let i=An(e)?gt(e.index,t):t;zs(i,5);let s=t[Te],a=ip(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=ip(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function ip(e,t,n,r){let o=k(null);try{return $(6,t,n),n(r)!==!1}catch(i){return gS(e,i),!1}finally{$(7,t,n),k(o)}}function gS(e,t){let n=e[ht],r=n?n.get(mt,null):null;r&&r.handleError(t)}function sp(e,t,n,r,o,i){let s=t[n],a=t[M],l=a.data[n].outputs[r],u=s[l],f=a.firstCreatePass?bu(a):null,h=_u(t),d=u.subscribe(i),p=h.length;h.push(i,d),f&&f.push(o,e.index,p,-(p+1))}function Ce(e,t,n,r){let o=H(),i=ge(),s=Ve();return av(i,o,o[Q],s,e,t,r),Ce}function mS(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[ls],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function av(e,t,n,r,o,i,s){let a=Eu(r),l=e.firstCreatePass?bu(e):null,u=_u(t),f=!0;if(r.type&3||s){let h=Dt(r,t),d=s?s(h):h,p=u.length,v=s?L=>s(ot(L[r.index])):r.index,E=null;if(!s&&a&&(E=mS(e,t,o,r.index)),E!==null){let L=E.__ngLastListenerFn__||E;L.__ngNextListenerFn__=i,E.__ngLastListenerFn__=i,f=!1}else{i=op(r,t,i),D_(t,d,o,i);let L=n.listen(d,o,i);u.push(i,L),l&&l.push(o,v,p,p+1)}}else i=op(r,t,i);if(f){let h=r.outputs?.[o],d=r.hostDirectiveOutputs?.[o];if(d&&d.length)for(let p=0;p<d.length;p+=2){let v=d[p],E=d[p+1];sp(r,t,v,E,o,i)}if(h&&h.length)for(let p of h)sp(r,t,p,o,o,i)}}function kn(e=1){return ew(e)}function cd(e,t,n){GI(e,t,n)}function ld(e){let t=H(),n=ge(),r=eg();Mu(r+1);let o=id(n,r);if(e.dirty&&PE(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=ZI(t,r);e.reset(i,Tw),e.notifyOnChanges()}return!0}return!1}function ud(){return HI(H(),eg())}function Z(e,t=""){let n=H(),r=ge(),o=e+ue,i=r.firstCreatePass?rd(r,o,1,t,null):r.data[o],s=cv(r,n,i,t,e);n[o]=s,Au()&&ed(r,n,s,i),mo(i,!1)}var cv=(e,t,n,r,o)=>(Jt(!0),rm(t[Q],r));function vS(e,t,n,r,o){let i=t[ft],s=!i||go()||Er(n)||Co(i,o);return Jt(s),s?rm(t[Q],r):Gs(i,e,t,n)}function yS(){cv=vS}function ct(e){return Ir("",e,""),ct}function Ir(e,t,n){let r=H(),o=LM(r,e,t,n);return o!==Pt&&DS(r,xn(),o),Ir}function DS(e,t,n){let r=Gp(t,e);K_(e[Q],r,n)}function Qs(e,t,n){Sg(t)&&(t=t());let r=H(),o=Iu();if(Mn(r,o,t)){let i=ge(),s=sg();pm(i,s,r,e,t,r[Q],n,!1)}return Qs}function dd(e,t){let n=Sg(e);return n&&e.set(t),n}function Ks(e,t){let n=H(),r=ge(),o=Ve();return av(r,n,n[Q],o,e,t),Ks}function CS(e,t,n){let r=ge();if(r.firstCreatePass){let o=pt(e);su(n,r.data,r.blueprint,o,!0),su(t,r.data,r.blueprint,o,!1)}}function su(e,t,n,r,o){if(e=ve(e),Array.isArray(e))for(let i=0;i<e.length;i++)su(e[i],t,n,r,o);else{let i=ge(),s=H(),a=Ve(),c=dr(e)?e:ve(e.provide),l=kp(e),u=a.providerIndexes&1048575,f=a.directiveStart,h=a.providerIndexes>>20;if(dr(e)||!e.multi){let d=new _n(l,o,B),p=gl(c,t,o?u:u+h,f);p===-1?(Tl(ms(a,s),i,c),pl(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(d),s.push(d)):(n[p]=d,s[p]=d)}else{let d=gl(c,t,u+h,f),p=gl(c,t,u,u+h),v=d>=0&&n[d],E=p>=0&&n[p];if(o&&!E||!o&&!v){Tl(ms(a,s),i,c);let L=_S(o?wS:ES,n.length,o,r,l);!o&&E&&(n[p].providerFactory=L),pl(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(L),s.push(L)}else{let L=lv(n[o?p:d],l,!o&&r);pl(i,e,d>-1?d:p,L)}!o&&r&&E&&n[p].componentProviders++}}}function pl(e,t,n,r){let o=dr(t),i=CE(t);if(o||i){let c=(i?ve(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function lv(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function gl(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function ES(e,t,n,r,o){return au(this.multi,[])}function wS(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=so(r,r[M],this.providerFactory.index,o);s=c.slice(0,a),au(i,s);for(let l=a;l<c.length;l++)s.push(c[l])}else s=[],au(i,s);return s}function au(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function _S(e,t,n,r,o){let i=new _n(e,n,B);return i.multi=[],i.index=t,i.componentProviders=0,lv(i,o,r&&!n),i}function wo(e,t=[]){return n=>{n.providersResolver=(r,o)=>CS(r,o?o(e):e,t)}}function bS(e,t){let n=e[t];return n===Pt?void 0:n}function IS(e,t,n,r,o,i,s){let a=t+n;return uM(e,a,o,i)?lM(e,a+2,s?r.call(s,o,i):r(o,i)):bS(e,a+2)}function uv(e,t){let n=ge(),r,o=e+ue;n.firstCreatePass?(r=MS(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=vn(r.type,!0)),s,a=be(B);try{let c=gs(!1),l=i();return gs(c),OE(n,H(),o,l),l}finally{be(a)}}function MS(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function dv(e,t,n,r){let o=e+ue,i=H(),s=RE(i,o);return SS(i,o)?IS(i,WE(),t,s.transform,n,r,s):s.transform(n,r)}function SS(e,t){return e[M].data[t].pure}var cu=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},fv=(()=>{class e{compileModuleSync(n){return new tu(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Np(n),i=tm(o.declarations).reduce((s,a)=>{let c=Dn(a);return c&&s.push(new co(c)),s},[]);return new cu(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var TS=(()=>{class e{zone=g(ee);changeDetectionScheduler=g(bn);applicationRef=g(Ae);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),AS=new y("",{factory:()=>!1});function hv({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new ee(O(m({},gv()),{scheduleInRootZone:n})),[{provide:ee,useFactory:e},{provide:Zt,multi:!0,useFactory:()=>{let r=g(TS,{optional:!0});return()=>r.initialize()}},{provide:Zt,multi:!0,useFactory:()=>{let r=g(NS);return()=>{r.initialize()}}},t===!0?{provide:wg,useValue:!0}:[],{provide:_g,useValue:n??Eg}]}function pv(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=hv({ngZoneFactory:()=>{let o=gv(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&Xt("NgZone_CoalesceEvent"),new ee(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return Kt([{provide:AS,useValue:!0},{provide:Fs,useValue:!1},r])}function gv(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var NS=(()=>{class e{subscription=new ne;initialized=!1;zone=g(ee);pendingTasks=g(st);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{ee.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{ee.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var xS=(()=>{class e{appRef=g(Ae);taskService=g(st);ngZone=g(ee);zonelessEnabled=g(Fs);tracing=g(Cr,{optional:!0});disableScheduling=g(wg,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new ne;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(ys):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(g(_g,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Ol||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Ah:bg;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(ys+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Ah(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function RS(){return typeof $localize<"u"&&$localize.locale||Ts}var Js=new y("",{providedIn:"root",factory:()=>g(Js,x.Optional|x.SkipSelf)||RS()});var lu=new y(""),OS=new y("");function Jr(e){return!e.moduleRef}function PS(e){let t=Jr(e)?e.r3Injector:e.moduleRef.injector,n=t.get(ee);return n.run(()=>{Jr(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(mt,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Jr(e)){let i=()=>t.destroy(),s=e.platformInjector.get(lu);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(lu);s.add(i),e.moduleRef.onDestroy(()=>{ts(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return FS(r,n,()=>{let i=t.get(Jm);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(Js,Ts);if(pS(s||Ts),!t.get(OS,!0))return Jr(e)?t.get(Ae):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Jr(e)){let c=t.get(Ae);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return kS(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function kS(e,t){let n=e.injector.get(Ae);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new D(-403,!1);t.push(e)}function FS(e,t,n){try{let r=n();return On(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var ns=null;function LS(e=[],t){return Ge.create({name:t,providers:[{provide:Rs,useValue:"platform"},{provide:lu,useValue:new Set([()=>ns=null])},...e]})}function VS(e=[]){if(ns)return ns;let t=LS(e);return ns=t,_M(),jS(t),t}function jS(e){let t=e.get(Ou,null);Le(e,()=>{t?.forEach(n=>n())})}var _o=(()=>{class e{static __NG_ELEMENT_ID__=BS}return e})();function BS(e){return US(Ve(),H(),(e&16)===16)}function US(e,t,n){if(An(e)&&!n){let r=gt(e.index,t);return new ao(r,r)}else if(e.type&175){let r=t[ze];return new ao(r,t)}return null}var uu=class{constructor(){}supports(t){return Wm(t)}create(t){return new du(t)}},$S=(e,t)=>t,du=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||$S}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<ap(r,o,i)?n:r,a=ap(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,u=c-o;if(l!=u){for(let h=0;h<l;h++){let d=h<i.length?i[h]:i[h]=0,p=d+h;u<=p&&p<l&&(i[h]=d+1)}let f=s.previousIndex;i[f]=u-l}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Wm(t))throw new D(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,aM(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new fu(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new As),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new As),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},fu=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},hu=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},As=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new hu,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function ap(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function cp(){return new fd([new uu])}var fd=(()=>{class e{factories;static \u0275prov=C({token:e,providedIn:"root",factory:cp});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||cp()),deps:[[e,new dE,new Mp]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new D(901,!1)}}return e})();function mv(e){$(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=VS(r),i=[hv({}),{provide:bn,useExisting:xS},...n||[]],s=new Ms({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return PS({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{$(9)}}var Yi=new WeakSet,lp="",rs=[];function up(e){return e.get($g,Qw)}function vv(){let e=[{provide:$g,useFactory:()=>{let t=!0;{let n=g(At);t=!!window._ejsas?.[n]}return t&&Xt("NgEventReplay"),t}}];return e.push({provide:Zt,useValue:()=>{let t=g(Ae),{injector:n}=t;if(!Yi.has(t)){let r=g(Ph);if(up(n)){C_();let o=n.get(At),i=y_(o,(s,a,c)=>{s.nodeType===Node.ELEMENT_NODE&&(p_(s,a,c),g_(s,r))});t.onDestroy(i)}}},multi:!0},{provide:Pn,useFactory:()=>{let t=g(Ae),{injector:n}=t;return()=>{!up(n)||Yi.has(t)||(Yi.add(t),t.onDestroy(()=>{Yi.delete(t);{let r=n.get(At);Kc(r)}}),t.whenStable().then(()=>{if(t.destroyed)return;let r=n.get(v_);HS(r,n);let o=n.get(Ph);o.get(lp)?.forEach(m_),o.delete(lp);let i=r.instance;N_(n)?t.onDestroy(()=>i.cleanUp()):i.cleanUp()}))}},multi:!0}),e}var HS=(e,t)=>{let n=t.get(At),r=window._ejsas[n],o=e.instance=new hh(new zi(r.c));for(let a of r.et)o.addEvent(a);for(let a of r.etc)o.addEvent(a);let i=ph(n);o.replayEarlyEventInfos(i),Kc(n);let s=new Gi(a=>{zS(t,a,a.currentTarget)});fh(o,s)};function zS(e,t,n){let r=(n&&n.getAttribute(Bs))??"";/d\d+/.test(r)?GS(r,e,t,n):t.eventPhase===Qc.REPLAY&&Wg(t,n)}function GS(e,t,n,r){rs.push({event:n,currentTarget:r}),NM(t,e,qS)}function qS(e){let t=[...rs],n=new Set(e);rs=[];for(let{event:r,currentTarget:o}of t){let i=o.getAttribute(Bs);n.has(i)?Wg(r,o):rs.push({event:r,currentTarget:o})}}var dp=!1;function WS(){dp||(dp=!0,M_(),sS(),yS(),lS(),pM(),VI(),dI(),sb())}function ZS(e){return e.whenStable()}function yv(){let e=[{provide:Wi,useFactory:()=>{let t=!0;return t=!!g(Dr,{optional:!0})?.get(Yg,null),t&&Xt("NgHydration"),t}},{provide:Zt,useValue:()=>{oI(!1),g(Wi)&&(P_(Ls()),WS())},multi:!0}];return e.push({provide:Ug,useFactory:()=>g(Wi)},{provide:Pn,useFactory:()=>{if(g(Wi)){let t=g(Ae);return()=>{ZS(t).then(()=>{t.destroyed||Rm(t)})}}return()=>{}},multi:!0}),Kt(e)}function hd(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function rn(e){return Mc(e)}function bo(e,t){return wc(e,t?.equal)}var fp=class{[$e];constructor(t){this[$e]=t}destroy(){this[$e].destroy()}};var he=new y("");var Ev=null;function Qe(){return Ev}function pd(e){Ev??=e}var Io=class{},gd=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>g(wv),providedIn:"platform"})}return e})();var wv=(()=>{class e extends gd{_location;_history;_doc=g(he);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Qe().getBaseHref(this._doc)}onPopState(n){let r=Qe().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Qe().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function _v(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Dv(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function on(e){return e&&e[0]!=="?"?`?${e}`:e}var Xs=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>g(Iv),providedIn:"root"})}return e})(),bv=new y(""),Iv=(()=>{class e extends Xs{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??g(he).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return _v(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+on(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+on(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+on(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(w(gd),w(bv,8))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Mr=(()=>{class e{_subject=new W;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=KS(Dv(Cv(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+on(r))}normalize(n){return e.stripTrailingSlash(QS(this._basePath,Cv(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+on(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+on(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=on;static joinWithSlash=_v;static stripTrailingSlash=Dv;static \u0275fac=function(r){return new(r||e)(w(Xs))};static \u0275prov=C({token:e,factory:()=>YS(),providedIn:"root"})}return e})();function YS(){return new Mr(w(Xs))}function QS(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Cv(e){return e.replace(/\/index.html$/,"")}function KS(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var me=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(me||{}),z=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(z||{}),Ne=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Ne||{}),Lt={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function Av(e){return Ye(e)[te.LocaleId]}function Nv(e,t,n){let r=Ye(e),o=[r[te.DayPeriodsFormat],r[te.DayPeriodsStandalone]],i=Ke(o,t);return Ke(i,n)}function xv(e,t,n){let r=Ye(e),o=[r[te.DaysFormat],r[te.DaysStandalone]],i=Ke(o,t);return Ke(i,n)}function Rv(e,t,n){let r=Ye(e),o=[r[te.MonthsFormat],r[te.MonthsStandalone]],i=Ke(o,t);return Ke(i,n)}function Ov(e,t){let r=Ye(e)[te.Eras];return Ke(r,t)}function Mo(e,t){let n=Ye(e);return Ke(n[te.DateFormat],t)}function So(e,t){let n=Ye(e);return Ke(n[te.TimeFormat],t)}function To(e,t){let r=Ye(e)[te.DateTimeFormat];return Ke(r,t)}function Ao(e,t){let n=Ye(e),r=n[te.NumberSymbols][t];if(typeof r>"u"){if(t===Lt.CurrencyDecimal)return n[te.NumberSymbols][Lt.Decimal];if(t===Lt.CurrencyGroup)return n[te.NumberSymbols][Lt.Group]}return r}function Pv(e){if(!e[te.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[te.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function kv(e){let t=Ye(e);return Pv(t),(t[te.ExtraData][2]||[]).map(r=>typeof r=="string"?md(r):[md(r[0]),md(r[1])])}function Fv(e,t,n){let r=Ye(e);Pv(r);let o=[r[te.ExtraData][0],r[te.ExtraData][1]],i=Ke(o,t)||[];return Ke(i,n)||[]}function Ke(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function md(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var JS=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,ea={},XS=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function Lv(e,t,n,r){let o=c0(e);t=Ft(n,t)||t;let s=[],a;for(;t;)if(a=XS.exec(t),a){s=s.concat(a.slice(1));let u=s.pop();if(!u)break;t=u}else{s.push(t);break}let c=o.getTimezoneOffset();r&&(c=jv(r,c),o=a0(o,r));let l="";return s.forEach(u=>{let f=i0(u);l+=f?f(o,n,c):u==="''"?"'":u.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),l}function ia(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function Ft(e,t){let n=Av(e);if(ea[n]??={},ea[n][t])return ea[n][t];let r="";switch(t){case"shortDate":r=Mo(e,Ne.Short);break;case"mediumDate":r=Mo(e,Ne.Medium);break;case"longDate":r=Mo(e,Ne.Long);break;case"fullDate":r=Mo(e,Ne.Full);break;case"shortTime":r=So(e,Ne.Short);break;case"mediumTime":r=So(e,Ne.Medium);break;case"longTime":r=So(e,Ne.Long);break;case"fullTime":r=So(e,Ne.Full);break;case"short":let o=Ft(e,"shortTime"),i=Ft(e,"shortDate");r=ta(To(e,Ne.Short),[o,i]);break;case"medium":let s=Ft(e,"mediumTime"),a=Ft(e,"mediumDate");r=ta(To(e,Ne.Medium),[s,a]);break;case"long":let c=Ft(e,"longTime"),l=Ft(e,"longDate");r=ta(To(e,Ne.Long),[c,l]);break;case"full":let u=Ft(e,"fullTime"),f=Ft(e,"fullDate");r=ta(To(e,Ne.Full),[u,f]);break}return r&&(ea[n][t]=r),r}function ta(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function lt(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function e0(e,t){return lt(e,3).substring(0,t)}function oe(e,t,n=0,r=!1,o=!1){return function(i,s){let a=t0(e,i);if((n>0||a>-n)&&(a+=n),e===3)a===0&&n===-12&&(a=12);else if(e===6)return e0(a,t);let c=Ao(s,Lt.MinusSign);return lt(a,t,c,r,o)}}function t0(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function Y(e,t,n=me.Format,r=!1){return function(o,i){return n0(o,i,e,t,n,r)}}function n0(e,t,n,r,o,i){switch(n){case 2:return Rv(t,o,r)[e.getMonth()];case 1:return xv(t,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let l=kv(t),u=Fv(t,o,r),f=l.findIndex(h=>{if(Array.isArray(h)){let[d,p]=h,v=s>=d.hours&&a>=d.minutes,E=s<p.hours||s===p.hours&&a<p.minutes;if(d.hours<p.hours){if(v&&E)return!0}else if(v||E)return!0}else if(h.hours===s&&h.minutes===a)return!0;return!1});if(f!==-1)return u[f]}return Nv(t,o,r)[s<12?0:1];case 3:return Ov(t,r)[e.getFullYear()<=0?0:1];default:let c=n;throw new Error(`unexpected translation type ${c}`)}}function na(e){return function(t,n,r){let o=-1*r,i=Ao(n,Lt.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+lt(s,2,i)+lt(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+lt(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+lt(s,2,i)+":"+lt(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+lt(s,2,i)+":"+lt(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var r0=0,oa=4;function o0(e){let t=ia(e,r0,1).getDay();return ia(e,0,1+(t<=oa?oa:oa+7)-t)}function Vv(e){let t=e.getDay(),n=t===0?-3:oa-t;return ia(e.getFullYear(),e.getMonth(),e.getDate()+n)}function vd(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=Vv(n),s=o0(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return lt(o,e,Ao(r,Lt.MinusSign))}}function ra(e,t=!1){return function(n,r){let i=Vv(n).getFullYear();return lt(i,e,Ao(r,Lt.MinusSign),t)}}var yd={};function i0(e){if(yd[e])return yd[e];let t;switch(e){case"G":case"GG":case"GGG":t=Y(3,z.Abbreviated);break;case"GGGG":t=Y(3,z.Wide);break;case"GGGGG":t=Y(3,z.Narrow);break;case"y":t=oe(0,1,0,!1,!0);break;case"yy":t=oe(0,2,0,!0,!0);break;case"yyy":t=oe(0,3,0,!1,!0);break;case"yyyy":t=oe(0,4,0,!1,!0);break;case"Y":t=ra(1);break;case"YY":t=ra(2,!0);break;case"YYY":t=ra(3);break;case"YYYY":t=ra(4);break;case"M":case"L":t=oe(1,1,1);break;case"MM":case"LL":t=oe(1,2,1);break;case"MMM":t=Y(2,z.Abbreviated);break;case"MMMM":t=Y(2,z.Wide);break;case"MMMMM":t=Y(2,z.Narrow);break;case"LLL":t=Y(2,z.Abbreviated,me.Standalone);break;case"LLLL":t=Y(2,z.Wide,me.Standalone);break;case"LLLLL":t=Y(2,z.Narrow,me.Standalone);break;case"w":t=vd(1);break;case"ww":t=vd(2);break;case"W":t=vd(1,!0);break;case"d":t=oe(2,1);break;case"dd":t=oe(2,2);break;case"c":case"cc":t=oe(7,1);break;case"ccc":t=Y(1,z.Abbreviated,me.Standalone);break;case"cccc":t=Y(1,z.Wide,me.Standalone);break;case"ccccc":t=Y(1,z.Narrow,me.Standalone);break;case"cccccc":t=Y(1,z.Short,me.Standalone);break;case"E":case"EE":case"EEE":t=Y(1,z.Abbreviated);break;case"EEEE":t=Y(1,z.Wide);break;case"EEEEE":t=Y(1,z.Narrow);break;case"EEEEEE":t=Y(1,z.Short);break;case"a":case"aa":case"aaa":t=Y(0,z.Abbreviated);break;case"aaaa":t=Y(0,z.Wide);break;case"aaaaa":t=Y(0,z.Narrow);break;case"b":case"bb":case"bbb":t=Y(0,z.Abbreviated,me.Standalone,!0);break;case"bbbb":t=Y(0,z.Wide,me.Standalone,!0);break;case"bbbbb":t=Y(0,z.Narrow,me.Standalone,!0);break;case"B":case"BB":case"BBB":t=Y(0,z.Abbreviated,me.Format,!0);break;case"BBBB":t=Y(0,z.Wide,me.Format,!0);break;case"BBBBB":t=Y(0,z.Narrow,me.Format,!0);break;case"h":t=oe(3,1,-12);break;case"hh":t=oe(3,2,-12);break;case"H":t=oe(3,1);break;case"HH":t=oe(3,2);break;case"m":t=oe(4,1);break;case"mm":t=oe(4,2);break;case"s":t=oe(5,1);break;case"ss":t=oe(5,2);break;case"S":t=oe(6,1);break;case"SS":t=oe(6,2);break;case"SSS":t=oe(6,3);break;case"Z":case"ZZ":case"ZZZ":t=na(0);break;case"ZZZZZ":t=na(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=na(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=na(2);break;default:return null}return yd[e]=t,t}function jv(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function s0(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function a0(e,t,n){let o=e.getTimezoneOffset(),i=jv(t,o);return s0(e,-1*(i-o))}function c0(e){if(Mv(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return ia(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(JS))return l0(r)}let t=new Date(e);if(!Mv(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function l0(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,c=Number(e[6]||0),l=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,c,l),t}function Mv(e){return e instanceof Date&&!isNaN(e.valueOf())}var sa=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},ca=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new sa(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Sv(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Sv(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(B(en),B(In),B(fd))};static \u0275dir=De({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Sv(e,t){e.context.$implicit=t.item}var Dd=(()=>{class e{_viewContainer;_context=new aa;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Tv(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Tv(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(B(en),B(In))};static \u0275dir=De({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),aa=class{$implicit=null;ngIf=null};function Tv(e,t){if(e&&!e.createEmbeddedView)throw new D(2020,!1)}function u0(e,t){return new D(2100,!1)}var d0="mediumDate",Bv=new y(""),Uv=new y(""),Cd=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??d0,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return Lv(n,s,i||this.locale,a)}catch(s){throw u0(e,s.message)}}static \u0275fac=function(r){return new(r||e)(B(Js,16),B(Bv,24),B(Uv,24))};static \u0275pipe=zm({name:"date",type:e,pure:!0})}return e})();var la=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=tn({type:e});static \u0275inj=Qt({})}return e})();function No(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var ua="browser",$v="server";function sn(e){return e===ua}function da(e){return e===$v}var Fn=class{};var pa=new y(""),bd=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new D(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(w(pa),w(ee))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),xo=class{_doc;constructor(t){this._doc=t}manager},fa="ng-app-id";function Hv(e){for(let t of e)t.remove()}function zv(e,t){let n=t.createElement("style");return n.textContent=e,n}function h0(e,t,n,r){let o=e.head?.querySelectorAll(`style[${fa}="${t}"],link[${fa}="${t}"]`);if(o)for(let i of o)i.removeAttribute(fa),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function wd(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var Id=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=da(i),h0(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,zv);r?.forEach(o=>this.addUsage(o,this.external,wd))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(Hv(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])Hv(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,zv(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,wd(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(fa,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(w(he),w(At),w(Pu,8),w(at))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),Ed={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Md=/%COMP%/g;var qv="%COMP%",p0=`_nghost-${qv}`,g0=`_ngcontent-${qv}`,m0=!0,v0=new y("",{providedIn:"root",factory:()=>m0});function y0(e){return g0.replace(Md,e)}function D0(e){return p0.replace(Md,e)}function Wv(e,t){return t.map(n=>n.replace(Md,e))}var Sd=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,l=null,u=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.tracingService=u,this.platformIsServer=da(a),this.defaultRenderer=new Ro(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===vt.ShadowDom&&(r=O(m({},r),{encapsulation:vt.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof ha?o.applyToHost(n):o instanceof Oo&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,f=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case vt.Emulated:i=new ha(c,l,r,this.appId,u,s,a,f,h);break;case vt.ShadowDom:return new _d(c,l,n,r,s,a,this.nonce,f,h);default:i=new Oo(c,l,r,u,s,a,f,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(w(bd),w(Id),w(At),w(v0),w(he),w(at),w(ee),w(Pu),w(Cr,8))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),Ro=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(Ed[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Gv(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Gv(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new D(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=Ed[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=Ed[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(Ot.DashCase|Ot.Important)?t.style.setProperty(n,r,o&Ot.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&Ot.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=Qe().getGlobalEventTarget(this.doc,t),!t))throw new D(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function Gv(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var _d=class extends Ro{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,c,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=o.styles;u=Wv(o.id,u);for(let h of u){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=h,this.shadowRoot.appendChild(d)}let f=o.getExternalStyles?.();if(f)for(let h of f){let d=wd(h,i);a&&d.setAttribute("nonce",a),this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Oo=class extends Ro{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let u=r.styles;this.styles=l?Wv(l,u):u,this.styleUrls=r.getExternalStyles?.(l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},ha=class extends Oo{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,l){let u=o+"-"+r.id;super(t,n,r,i,s,a,c,l,u),this.contentAttr=y0(u),this.hostAttr=D0(u)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var ga=class e extends Io{supportsDOMEvents=!0;static makeCurrent(){pd(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=C0();return n==null?null:E0(n)}resetBaseElement(){Po=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return No(document.cookie,t)}},Po=null;function C0(){return Po=Po||document.head.querySelector("base"),Po?Po.getAttribute("href"):null}function E0(e){return new URL(e,document.baseURI).pathname}var w0=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),Yv=(()=>{class e extends xo{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(w(he))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),Zv=["alt","control","meta","shift"],_0={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},b0={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},Qv=(()=>{class e extends xo{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Qe().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Zv.forEach(l=>{let u=r.indexOf(l);u>-1&&(r.splice(u,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=_0[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Zv.forEach(s=>{if(s!==o){let a=b0[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(w(he))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})();function Td(e,t){return mv(m({rootComponent:e},I0(t)))}function I0(e){return{appProviders:[...N0,...e?.providers??[]],platformProviders:A0}}function M0(){ga.makeCurrent()}function S0(){return new mt}function T0(){return kg(document),document}var A0=[{provide:at,useValue:ua},{provide:Ou,useValue:M0,multi:!0},{provide:he,useFactory:T0}];var N0=[{provide:Rs,useValue:"root"},{provide:mt,useFactory:S0},{provide:pa,useClass:Yv,multi:!0,deps:[he]},{provide:pa,useClass:Qv,multi:!0,deps:[he]},Sd,Id,bd,{provide:hr,useExisting:Sd},{provide:Fn,useClass:w0},[]];var Tr=class{},ko=class{},Je=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var va=class{encodeKey(t){return Kv(t)}encodeValue(t){return Kv(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function x0(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var R0=/%(\d[a-f0-9])/gi,O0={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Kv(e){return encodeURIComponent(e).replace(R0,(t,n)=>O0[n]??t)}function ma(e){return`${e}`}var Vt=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new va,t.fromString){if(t.fromObject)throw new D(2805,!1);this.map=x0(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(ma):[ma(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(ma(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(ma(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var ya=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function P0(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Jv(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function Xv(e){return typeof Blob<"u"&&e instanceof Blob}function ey(e){return typeof FormData<"u"&&e instanceof FormData}function k0(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var ty="Content-Type",ny="Accept",oy="X-Request-URL",iy="text/plain",sy="application/json",F0=`${sy}, ${iy}, */*`,Sr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(P0(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new Je,this.context??=new ya,!this.params)this.params=new Vt,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Jv(this.body)||Xv(this.body)||ey(this.body)||k0(this.body)?this.body:this.body instanceof Vt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||ey(this.body)?null:Xv(this.body)?this.body.type||null:Jv(this.body)?null:typeof this.body=="string"?iy:this.body instanceof Vt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?sy:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,l=t.headers||this.headers,u=t.params||this.params,f=t.context??this.context;return t.setHeaders!==void 0&&(l=Object.keys(t.setHeaders).reduce((h,d)=>h.set(d,t.setHeaders[d]),l)),t.setParams&&(u=Object.keys(t.setParams).reduce((h,d)=>h.set(d,t.setParams[d]),u)),new e(n,r,s,{params:u,headers:l,context:f,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},Ln=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Ln||{}),Ar=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new Je,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Da=class e extends Ar{constructor(t={}){super(t)}type=Ln.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Vn=class e extends Ar{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=Ln.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Fo=class extends Ar{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},L0=200,V0=204;function Ad(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var Ea=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof Sr)i=n;else{let c;o.headers instanceof Je?c=o.headers:c=new Je(o.headers);let l;o.params&&(o.params instanceof Vt?l=o.params:l=new Vt({fromObject:o.params})),i=new Sr(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:l,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=b(i).pipe(Ht(c=>this.handler.handle(c)));if(n instanceof Sr||o.observe==="events")return s;let a=s.pipe(Oe(c=>c instanceof Vn));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(I(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new D(2806,!1);return c.body}));case"blob":return a.pipe(I(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new D(2807,!1);return c.body}));case"text":return a.pipe(I(c=>{if(c.body!==null&&typeof c.body!="string")throw new D(2808,!1);return c.body}));case"json":default:return a.pipe(I(c=>c.body))}case"response":return a;default:throw new D(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new Vt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,Ad(o,r))}post(n,r,o={}){return this.request("POST",n,Ad(o,r))}put(n,r,o={}){return this.request("PUT",n,Ad(o,r))}static \u0275fac=function(r){return new(r||e)(w(Tr))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})();var j0=new y("");function ay(e,t){return t(e)}function B0(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}function U0(e,t,n){return(r,o)=>Le(n,()=>t(r,i=>e(i,o)))}var cy=new y(""),xd=new y(""),Rd=new y(""),Od=new y("",{providedIn:"root",factory:()=>!0});function $0(){let e=null;return(t,n)=>{e===null&&(e=(g(cy,{optional:!0})??[]).reduceRight(B0,ay));let r=g(st);if(g(Od)){let i=r.add();return e(t,n).pipe(Gt(()=>r.remove(i)))}else return e(t,n)}}var Ca=(()=>{class e extends Tr{backend;injector;chain=null;pendingTasks=g(st);contributeToStability=g(Od);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(xd),...this.injector.get(Rd,[])]));this.chain=r.reduceRight((o,i)=>U0(o,i,this.injector),ay)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(Gt(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(w(ko),w(ye))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})();var H0=/^\)\]\}',?\n/,z0=RegExp(`^${oy}:`,"m");function G0(e){return"responseURL"in e&&e.responseURL?e.responseURL:z0.test(e.getAllResponseHeaders())?e.getResponseHeader(oy):null}var Nd=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new D(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?K(r.\u0275loadImpl()):b(null)).pipe(Pe(()=>new V(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((v,E)=>s.setRequestHeader(v,E.join(","))),n.headers.has(ny)||s.setRequestHeader(ny,F0),!n.headers.has(ty)){let v=n.detectContentTypeHeader();v!==null&&s.setRequestHeader(ty,v)}if(n.responseType){let v=n.responseType.toLowerCase();s.responseType=v!=="json"?v:"text"}let a=n.serializeBody(),c=null,l=()=>{if(c!==null)return c;let v=s.statusText||"OK",E=new Je(s.getAllResponseHeaders()),L=G0(s)||n.url;return c=new Da({headers:E,status:s.status,statusText:v,url:L}),c},u=()=>{let{headers:v,status:E,statusText:L,url:zn}=l(),J=null;E!==V0&&(J=typeof s.response>"u"?s.responseText:s.response),E===0&&(E=J?L0:0);let zr=E>=200&&E<300;if(n.responseType==="json"&&typeof J=="string"){let xD=J;J=J.replace(H0,"");try{J=J!==""?JSON.parse(J):null}catch(RD){J=xD,zr&&(zr=!1,J={error:RD,text:J})}}zr?(i.next(new Vn({body:J,headers:v,status:E,statusText:L,url:zn||void 0})),i.complete()):i.error(new Fo({error:J,headers:v,status:E,statusText:L,url:zn||void 0}))},f=v=>{let{url:E}=l(),L=new Fo({error:v,status:s.status||0,statusText:s.statusText||"Unknown Error",url:E||void 0});i.error(L)},h=!1,d=v=>{h||(i.next(l()),h=!0);let E={type:Ln.DownloadProgress,loaded:v.loaded};v.lengthComputable&&(E.total=v.total),n.responseType==="text"&&s.responseText&&(E.partialText=s.responseText),i.next(E)},p=v=>{let E={type:Ln.UploadProgress,loaded:v.loaded};v.lengthComputable&&(E.total=v.total),i.next(E)};return s.addEventListener("load",u),s.addEventListener("error",f),s.addEventListener("timeout",f),s.addEventListener("abort",f),n.reportProgress&&(s.addEventListener("progress",d),a!==null&&s.upload&&s.upload.addEventListener("progress",p)),s.send(a),i.next({type:Ln.Sent}),()=>{s.removeEventListener("error",f),s.removeEventListener("abort",f),s.removeEventListener("load",u),s.removeEventListener("timeout",f),n.reportProgress&&(s.removeEventListener("progress",d),a!==null&&s.upload&&s.upload.removeEventListener("progress",p)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(w(Fn))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),ly=new y(""),q0="XSRF-TOKEN",W0=new y("",{providedIn:"root",factory:()=>q0}),Z0="X-XSRF-TOKEN",Y0=new y("",{providedIn:"root",factory:()=>Z0}),Lo=class{},Q0=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=No(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(w(he),w(W0))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})();function K0(e,t){let n=e.url.toLowerCase();if(!g(ly)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=g(Lo).getToken(),o=g(Y0);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var Pd=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Pd||{});function J0(e,t){return{\u0275kind:e,\u0275providers:t}}function kd(...e){let t=[Ea,Nd,Ca,{provide:Tr,useExisting:Ca},{provide:ko,useFactory:()=>g(j0,{optional:!0})??g(Nd)},{provide:xd,useValue:K0,multi:!0},{provide:ly,useValue:!0},{provide:Lo,useClass:Q0}];for(let n of e)t.push(...n.\u0275providers);return Kt(t)}var ry=new y("");function Fd(){return J0(Pd.LegacyInterceptors,[{provide:ry,useFactory:$0},{provide:xd,useExisting:ry,multi:!0}])}var X0=new y(""),eT="b",tT="h",nT="s",rT="st",oT="u",iT="rt",Ld=new y(""),sT=["GET","HEAD"];function aT(e,t){let h=g(Ld),{isCacheActive:n}=h,r=Df(h,["isCacheActive"]),{transferCache:o,method:i}=e;if(!n||o===!1||i==="POST"&&!r.includePostRequests&&!o||i!=="POST"&&!sT.includes(i)||!r.includeRequestsWithAuthHeaders&&cT(e)||r.filter?.(e)===!1)return t(e);let s=g(Dr);if(g(X0,{optional:!0}))throw new D(2803,!1);let c=e.url,l=lT(e,c),u=s.get(l,null),f=r.includeHeaders;if(typeof o=="object"&&o.includeHeaders&&(f=o.includeHeaders),u){let{[eT]:d,[iT]:p,[tT]:v,[nT]:E,[rT]:L,[oT]:zn}=u,J=d;switch(p){case"arraybuffer":J=new TextEncoder().encode(d).buffer;break;case"blob":J=new Blob([d]);break}let zr=new Je(v);return b(new Vn({body:J,headers:zr,status:E,statusText:L,url:zn}))}return t(e).pipe(ae(d=>{d instanceof Vn}))}function cT(e){return e.headers.has("authorization")||e.headers.has("proxy-authorization")}function uy(e){return[...e.keys()].sort().map(t=>`${t}=${e.getAll(t)}`).join("&")}function lT(e,t){let{params:n,method:r,responseType:o}=e,i=uy(n),s=e.serializeBody();s instanceof URLSearchParams?s=uy(s):typeof s!="string"&&(s="");let a=[r,o,t,s,i].join("|"),c=uT(a);return c}function uT(e){let t=0;for(let n of e)t=Math.imul(31,t)+n.charCodeAt(0)<<0;return t+=2147483648,t.toString()}function dy(e){return[{provide:Ld,useFactory:()=>(Xt("NgHttpTransferCache"),m({isCacheActive:!0},e))},{provide:Rd,useValue:aT,multi:!0},{provide:Pn,multi:!0,useFactory:()=>{let t=g(Ae),n=g(Ld);return()=>{t.whenStable().then(()=>{n.isCacheActive=!1})}}}]}var fy=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(w(he))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var wa=function(e){return e[e.NoHttpTransferCache=0]="NoHttpTransferCache",e[e.HttpTransferCacheOptions=1]="HttpTransferCacheOptions",e[e.I18nSupport=2]="I18nSupport",e[e.EventReplay=3]="EventReplay",e[e.IncrementalHydration=4]="IncrementalHydration",e}(wa||{});function hT(e,t=[],n={}){return{\u0275kind:e,\u0275providers:t}}function hy(){return hT(wa.EventReplay,vv())}function py(...e){let t=[],n=new Set;for(let{\u0275providers:o,\u0275kind:i}of e)n.add(i),o.length&&t.push(o);let r=n.has(wa.HttpTransferCacheOptions);return Kt([[],yv(),n.has(wa.NoHttpTransferCache)||r?[]:dy({}),t])}var N="primary",Ko=Symbol("RouteTitle"),$d=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function Un(e){return new $d(e)}function wy(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function gT(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!Et(e[n],t[n]))return!1;return!0}function Et(e,t){let n=e?Hd(e):void 0,r=t?Hd(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!_y(e[o],t[o]))return!1;return!0}function Hd(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function _y(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function by(e){return e.length>0?e[e.length-1]:null}function ln(e){return Lc(e)?e:On(e)?K(Promise.resolve(e)):b(e)}var mT={exact:My,subset:Sy},Iy={exact:vT,subset:yT,ignored:()=>!0};function gy(e,t,n){return mT[n.paths](e.root,t.root,n.matrixParams)&&Iy[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function vT(e,t){return Et(e,t)}function My(e,t,n){if(!jn(e.segments,t.segments)||!Ia(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!My(e.children[r],t.children[r],n))return!1;return!0}function yT(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>_y(e[n],t[n]))}function Sy(e,t,n){return Ty(e,t,t.segments,n)}function Ty(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!jn(o,n)||t.hasChildren()||!Ia(o,n,r))}else if(e.segments.length===n.length){if(!jn(e.segments,n)||!Ia(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!Sy(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!jn(e.segments,o)||!Ia(e.segments,o,r)||!e.children[N]?!1:Ty(e.children[N],t,i,r)}}function Ia(e,t,n){return t.every((r,o)=>Iy[n](e[o].parameters,r.parameters))}var _t=class{root;queryParams;fragment;_queryParamMap;constructor(t=new U([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Un(this.queryParams),this._queryParamMap}toString(){return ET.serialize(this)}},U=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ma(this)}},an=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=Un(this.parameters),this._parameterMap}toString(){return Ny(this)}};function DT(e,t){return jn(e,t)&&e.every((n,r)=>Et(n.parameters,t[r].parameters))}function jn(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function CT(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===N&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==N&&(n=n.concat(t(o,r)))}),n}var Jo=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>new $n,providedIn:"root"})}return e})(),$n=class{parse(t){let n=new Gd(t);return new _t(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${Vo(t.root,!0)}`,r=bT(t.queryParams),o=typeof t.fragment=="string"?`#${wT(t.fragment)}`:"";return`${n}${r}${o}`}},ET=new $n;function Ma(e){return e.segments.map(t=>Ny(t)).join("/")}function Vo(e,t){if(!e.hasChildren())return Ma(e);if(t){let n=e.children[N]?Vo(e.children[N],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==N&&r.push(`${o}:${Vo(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=CT(e,(r,o)=>o===N?[Vo(e.children[N],!1)]:[`${o}:${Vo(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[N]!=null?`${Ma(e)}/${n[0]}`:`${Ma(e)}/(${n.join("//")})`}}function Ay(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function _a(e){return Ay(e).replace(/%3B/gi,";")}function wT(e){return encodeURI(e)}function zd(e){return Ay(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Sa(e){return decodeURIComponent(e)}function my(e){return Sa(e.replace(/\+/g,"%20"))}function Ny(e){return`${zd(e.path)}${_T(e.parameters)}`}function _T(e){return Object.entries(e).map(([t,n])=>`;${zd(t)}=${zd(n)}`).join("")}function bT(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${_a(n)}=${_a(o)}`).join("&"):`${_a(n)}=${_a(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var IT=/^[^\/()?;#]+/;function Vd(e){let t=e.match(IT);return t?t[0]:""}var MT=/^[^\/()?;=#]+/;function ST(e){let t=e.match(MT);return t?t[0]:""}var TT=/^[^=?&#]+/;function AT(e){let t=e.match(TT);return t?t[0]:""}var NT=/^[^&#]+/;function xT(e){let t=e.match(NT);return t?t[0]:""}var Gd=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new U([],{}):new U([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[N]=new U(t,n)),r}parseSegment(){let t=Vd(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new D(4009,!1);return this.capture(t),new an(Sa(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=ST(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=Vd(this.remaining);o&&(r=o,this.capture(r))}t[Sa(n)]=Sa(r)}parseQueryParam(t){let n=AT(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=xT(this.remaining);s&&(r=s,this.capture(r))}let o=my(n),i=my(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Vd(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new D(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=N);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[N]:new U([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new D(4011,!1)}};function xy(e){return e.segments.length>0?new U([],{[N]:e}):e}function Ry(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=Ry(o);if(r===N&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new U(e.segments,t);return RT(n)}function RT(e){if(e.numberOfChildren===1&&e.children[N]){let t=e.children[N];return new U(e.segments.concat(t.segments),t.children)}return e}function Pr(e){return e instanceof _t}function Oy(e,t,n=null,r=null){let o=Py(e);return ky(o,t,n,r)}function Py(e){let t;function n(i){let s={};for(let c of i.children){let l=n(c);s[c.outlet]=l}let a=new U(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=xy(r);return t??o}function ky(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return jd(o,o,o,n,r);let i=OT(t);if(i.toRoot())return jd(o,o,new U([],{}),n,r);let s=PT(i,o,e),a=s.processChildren?Bo(s.segmentGroup,s.index,i.commands):Ly(s.segmentGroup,s.index,i.commands);return jd(o,s.segmentGroup,a,n,r)}function Aa(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function $o(e){return typeof e=="object"&&e!=null&&e.outlets}function jd(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(u=>`${u}`):`${l}`});let s;e===t?s=n:s=Fy(e,t,n);let a=xy(Ry(s));return new _t(a,i,o)}function Fy(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=Fy(i,t,n)}),new U(e.segments,r)}var Na=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Aa(r[0]))throw new D(4003,!1);let o=r.find($o);if(o&&o!==by(r))throw new D(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function OT(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Na(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Na(n,t,r)}var Rr=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function PT(e,t,n){if(e.isAbsolute)return new Rr(t,!0,0);if(!n)return new Rr(t,!1,NaN);if(n.parent===null)return new Rr(n,!0,0);let r=Aa(e.commands[0])?0:1,o=n.segments.length-1+r;return kT(n,o,e.numberOfDoubleDots)}function kT(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new D(4005,!1);o=r.segments.length}return new Rr(r,!1,o-i)}function FT(e){return $o(e[0])?e[0].outlets:{[N]:e}}function Ly(e,t,n){if(e??=new U([],{}),e.segments.length===0&&e.hasChildren())return Bo(e,t,n);let r=LT(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new U(e.segments.slice(0,r.pathIndex),{});return i.children[N]=new U(e.segments.slice(r.pathIndex),e.children),Bo(i,0,o)}else return r.match&&o.length===0?new U(e.segments,{}):r.match&&!e.hasChildren()?qd(e,t,n):r.match?Bo(e,0,o):qd(e,t,n)}function Bo(e,t,n){if(n.length===0)return new U(e.segments,{});{let r=FT(n),o={};if(Object.keys(r).some(i=>i!==N)&&e.children[N]&&e.numberOfChildren===1&&e.children[N].segments.length===0){let i=Bo(e.children[N],t,n);return new U(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Ly(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new U(e.segments,o)}}function LT(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if($o(a))break;let c=`${a}`,l=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!yy(c,l,s))return i;r+=2}else{if(!yy(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function qd(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if($o(i)){let c=VT(i.outlets);return new U(r,c)}if(o===0&&Aa(n[0])){let c=e.segments[t];r.push(new an(c.path,vy(n[0]))),o++;continue}let s=$o(i)?i.outlets[N]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Aa(a)?(r.push(new an(s,vy(a))),o+=2):(r.push(new an(s,{})),o++)}return new U(r,{})}function VT(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=qd(new U([],{}),0,r))}),t}function vy(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function yy(e,t,n){return e==n.path&&Et(t,n.parameters)}var Ta="imperative",pe=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(pe||{}),Ue=class{id;url;constructor(t,n){this.id=t,this.url=n}},Hn=class extends Ue{type=pe.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},jt=class extends Ue{urlAfterRedirects;type=pe.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},xe=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(xe||{}),Ho=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Ho||{}),wt=class extends Ue{reason;code;type=pe.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Bt=class extends Ue{reason;code;type=pe.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},kr=class extends Ue{error;target;type=pe.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},zo=class extends Ue{urlAfterRedirects;state;type=pe.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},xa=class extends Ue{urlAfterRedirects;state;type=pe.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ra=class extends Ue{urlAfterRedirects;state;shouldActivate;type=pe.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Oa=class extends Ue{urlAfterRedirects;state;type=pe.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Pa=class extends Ue{urlAfterRedirects;state;type=pe.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ka=class{route;type=pe.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Fa=class{route;type=pe.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},La=class{snapshot;type=pe.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Va=class{snapshot;type=pe.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ja=class{snapshot;type=pe.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ba=class{snapshot;type=pe.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Go=class{},Fr=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function jT(e,t){return e.providers&&!e._injector&&(e._injector=Eo(e.providers,t,`Route: ${e.path}`)),e._injector??t}function ut(e){return e.outlet||N}function BT(e,t){let n=e.filter(r=>ut(r)===t);return n.push(...e.filter(r=>ut(r)!==t)),n}function Xo(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var Ua=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Xo(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new jr(this.rootInjector)}},jr=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new Ua(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(w(ye))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),$a=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Wd(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Wd(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=Zd(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return Zd(t,this._root).map(n=>n.value)}};function Wd(e,t){if(e===t.value)return t;for(let n of t.children){let r=Wd(e,n);if(r)return r}return null}function Zd(e,t){if(e===t.value)return[t];for(let n of t.children){let r=Zd(e,n);if(r.length)return r.unshift(t),r}return[]}var Be=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function xr(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var qo=class extends $a{snapshot;constructor(t,n){super(t),this.snapshot=n,nf(this,t)}toString(){return this.snapshot.toString()}};function Vy(e){let t=UT(e),n=new X([new an("",{})]),r=new X({}),o=new X({}),i=new X({}),s=new X(""),a=new cn(n,r,i,s,o,N,e,t.root);return a.snapshot=t.root,new qo(new Be(a,[]),t)}function UT(e){let t={},n={},r={},o="",i=new Bn([],t,r,o,n,N,e,null,{});return new Wo("",new Be(i,[]))}var cn=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(I(l=>l[Ko]))??b(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(I(t=>Un(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(I(t=>Un(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Ha(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:m(m({},t.params),e.params),data:m(m({},t.data),e.data),resolve:m(m(m(m({},e.data),t.data),o?.data),e._resolvedData)}:r={params:m({},e.params),data:m({},e.data),resolve:m(m({},e.data),e._resolvedData??{})},o&&By(o)&&(r.resolve[Ko]=o.title),r}var Bn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Ko]}constructor(t,n,r,o,i,s,a,c,l){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Un(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Un(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},Wo=class extends $a{url;constructor(t,n){super(n),this.url=t,nf(this,n)}toString(){return jy(this._root)}};function nf(e,t){t.value._routerState=e,t.children.forEach(n=>nf(e,n))}function jy(e){let t=e.children.length>0?` { ${e.children.map(jy).join(", ")} } `:"";return`${e.value}${t}`}function Bd(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,Et(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),Et(t.params,n.params)||e.paramsSubject.next(n.params),gT(t.url,n.url)||e.urlSubject.next(n.url),Et(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Yd(e,t){let n=Et(e.params,t.params)&&DT(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Yd(e.parent,t.parent))}function By(e){return typeof e.title=="string"||e.title===null}var Uy=new y(""),ei=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=N;activateEvents=new de;deactivateEvents=new de;attachEvents=new de;detachEvents=new de;routerOutletData=Mg(void 0);parentContexts=g(jr);location=g(en);changeDetector=g(_o);inputBinder=g(Wa,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new D(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new D(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new D(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new D(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Qd(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=De({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[ho]})}return e})(),Qd=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===cn?this.route:t===jr?this.childContexts:t===Uy?this.outletData:this.parent.get(t,n)}},Wa=new y("");var rf=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=_r({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&fe(0,"router-outlet")},dependencies:[ei],encapsulation:2})}return e})();function of(e){let t=e.children&&e.children.map(of),n=t?O(m({},e),{children:t}):m({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==N&&(n.component=rf),n}function $T(e,t,n){let r=Zo(e,t._root,n?n._root:void 0);return new qo(r,t)}function Zo(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=HT(e,t,n);return new Be(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>Zo(e,a)),s}}let r=zT(t.value),o=t.children.map(i=>Zo(e,i));return new Be(r,o)}}function HT(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Zo(e,r,o);return Zo(e,r)})}function zT(e){return new cn(new X(e.url),new X(e.params),new X(e.queryParams),new X(e.fragment),new X(e.data),e.outlet,e.component,e)}var Lr=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},$y="ngNavigationCancelingError";function za(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=Pr(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Hy(!1,xe.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function Hy(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[$y]=!0,n.cancellationCode=t,n}function GT(e){return zy(e)&&Pr(e.url)}function zy(e){return!!e&&e[$y]}var qT=(e,t,n,r)=>I(o=>(new Kd(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),Kd=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),Bd(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=xr(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=xr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=xr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=xr(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new Ba(i.value.snapshot))}),t.children.length&&this.forwardEvent(new Va(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(Bd(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Bd(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},Ga=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},Or=class{component;route;constructor(t,n){this.component=t,this.route=n}};function WT(e,t,n){let r=e._root,o=t?t._root:null;return jo(r,o,n,[r.value])}function ZT(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function Br(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!vp(e)?e:t.get(e):r}function jo(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=xr(t);return e.children.forEach(s=>{YT(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Uo(a,n.getContext(s),o)),o}function YT(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=QT(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Ga(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?jo(e,t,a?a.children:null,r,o):jo(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Or(a.outlet.component,s))}else s&&Uo(t,a,o),o.canActivateChecks.push(new Ga(r)),i.component?jo(e,null,a?a.children:null,r,o):jo(e,null,n,r,o);return o}function QT(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!jn(e.url,t.url);case"pathParamsOrQueryParamsChange":return!jn(e.url,t.url)||!Et(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Yd(e,t)||!Et(e.queryParams,t.queryParams);case"paramsChange":default:return!Yd(e,t)}}function Uo(e,t,n){let r=xr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?Uo(s,t.children.getContext(i),n):Uo(s,null,n):Uo(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new Or(t.outlet.component,o)):n.canDeactivateChecks.push(new Or(null,o)):n.canDeactivateChecks.push(new Or(null,o))}function ti(e){return typeof e=="function"}function KT(e){return typeof e=="boolean"}function JT(e){return e&&ti(e.canLoad)}function XT(e){return e&&ti(e.canActivate)}function eA(e){return e&&ti(e.canActivateChild)}function tA(e){return e&&ti(e.canDeactivate)}function nA(e){return e&&ti(e.canMatch)}function Gy(e){return e instanceof Mt||e?.name==="EmptyError"}var ba=Symbol("INITIAL_VALUE");function Vr(){return Pe(e=>Vi(e.map(t=>t.pipe(St(1),Uc(ba)))).pipe(I(t=>{for(let n of t)if(n!==!0){if(n===ba)return ba;if(n===!1||rA(n))return n}return!0}),Oe(t=>t!==ba),St(1)))}function rA(e){return Pr(e)||e instanceof Lr}function oA(e,t){return se(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?b(O(m({},n),{guardsResult:!0})):iA(s,r,o,e).pipe(se(a=>a&&KT(a)?sA(r,i,e,t):b(a)),I(a=>O(m({},n),{guardsResult:a})))})}function iA(e,t,n,r){return K(e).pipe(se(o=>dA(o.component,o.route,n,t,r)),Tt(o=>o!==!0,!0))}function sA(e,t,n,r){return K(t).pipe(Ht(o=>Jn(cA(o.route.parent,r),aA(o.route,r),uA(e,o.path,n),lA(e,o.route,n))),Tt(o=>o!==!0,!0))}function aA(e,t){return e!==null&&t&&t(new ja(e)),b(!0)}function cA(e,t){return e!==null&&t&&t(new La(e)),b(!0)}function lA(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return b(!0);let o=r.map(i=>ji(()=>{let s=Xo(t)??n,a=Br(i,s),c=XT(a)?a.canActivate(t,e):Le(s,()=>a(t,e));return ln(c).pipe(Tt())}));return b(o).pipe(Vr())}function uA(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>ZT(s)).filter(s=>s!==null).map(s=>ji(()=>{let a=s.guards.map(c=>{let l=Xo(s.node)??n,u=Br(c,l),f=eA(u)?u.canActivateChild(r,e):Le(l,()=>u(r,e));return ln(f).pipe(Tt())});return b(a).pipe(Vr())}));return b(i).pipe(Vr())}function dA(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return b(!0);let s=i.map(a=>{let c=Xo(t)??o,l=Br(a,c),u=tA(l)?l.canDeactivate(e,t,n,r):Le(c,()=>l(e,t,n,r));return ln(u).pipe(Tt())});return b(s).pipe(Vr())}function fA(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return b(!0);let i=o.map(s=>{let a=Br(s,e),c=JT(a)?a.canLoad(t,n):Le(e,()=>a(t,n));return ln(c)});return b(i).pipe(Vr(),qy(r))}function qy(e){return Oc(ae(t=>{if(typeof t!="boolean")throw za(e,t)}),I(t=>t===!0))}function hA(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return b(!0);let i=o.map(s=>{let a=Br(s,e),c=nA(a)?a.canMatch(t,n):Le(e,()=>a(t,n));return ln(c)});return b(i).pipe(Vr(),qy(r))}var Yo=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},Qo=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function Nr(e){return It(new Yo(e))}function pA(e){return It(new D(4e3,!1))}function gA(e){return It(Hy(!1,xe.GuardRejected))}var Jd=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return b(r);if(o.numberOfChildren>1||!o.children[N])return pA(`${t.redirectTo}`);o=o.children[N]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:l,routeConfig:u,url:f,outlet:h,params:d,data:p,title:v}=o,E=Le(i,()=>a({params:d,data:p,queryParams:c,fragment:l,routeConfig:u,url:f,outlet:h,title:v}));if(E instanceof _t)throw new Qo(E);n=E}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new Qo(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new _t(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new U(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new D(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},Xd={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function mA(e,t,n,r,o){let i=Wy(e,t,n);return i.matched?(r=jT(t,r),hA(r,t,n,o).pipe(I(s=>s===!0?i:m({},Xd)))):b(i)}function Wy(e,t,n){if(t.path==="**")return vA(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?m({},Xd):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||wy)(n,e,t);if(!o)return m({},Xd);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?m(m({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function vA(e){return{matched:!0,parameters:e.length>0?by(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function Dy(e,t,n,r){return n.length>0&&CA(e,n,r)?{segmentGroup:new U(t,DA(r,new U(n,e.children))),slicedSegments:[]}:n.length===0&&EA(e,n,r)?{segmentGroup:new U(e.segments,yA(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new U(e.segments,e.children),slicedSegments:n}}function yA(e,t,n,r){let o={};for(let i of n)if(Za(e,t,i)&&!r[ut(i)]){let s=new U([],{});o[ut(i)]=s}return m(m({},r),o)}function DA(e,t){let n={};n[N]=t;for(let r of e)if(r.path===""&&ut(r)!==N){let o=new U([],{});n[ut(r)]=o}return n}function CA(e,t,n){return n.some(r=>Za(e,t,r)&&ut(r)!==N)}function EA(e,t,n){return n.some(r=>Za(e,t,r))}function Za(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function wA(e,t,n){return t.length===0&&!e.children[n]}var ef=class{};function _A(e,t,n,r,o,i,s="emptyOnly"){return new tf(e,t,n,r,o,s,i).recognize()}var bA=31,tf=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Jd(this.urlSerializer,this.urlTree)}noMatchError(t){return new D(4002,`'${t.segmentGroup}'`)}recognize(){let t=Dy(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(I(({children:n,rootSnapshot:r})=>{let o=new Be(r,n),i=new Wo("",o),s=Oy(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new Bn([],Object.freeze({}),Object.freeze(m({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),N,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,N,n).pipe(I(r=>({children:r,rootSnapshot:n})),_e(r=>{if(r instanceof Qo)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Yo?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(I(s=>s instanceof Be?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return K(i).pipe(Ht(s=>{let a=r.children[s],c=BT(n,s);return this.processSegmentGroup(t,c,a,s,o)}),Bc((s,a)=>(s.push(...a),s)),zt(null),jc(),se(s=>{if(s===null)return Nr(r);let a=Zy(s);return IA(a),b(a)}))}processSegment(t,n,r,o,i,s,a){return K(n).pipe(Ht(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(_e(l=>{if(l instanceof Yo)return b(null);throw l}))),Tt(c=>!!c),_e(c=>{if(Gy(c))return wA(r,o,i)?b(new ef):Nr(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return ut(r)!==s&&(s===N||!Za(o,i,r))?Nr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):Nr(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:l,consumedSegments:u,positionalParamSegments:f,remainingSegments:h}=Wy(n,o,i);if(!c)return Nr(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>bA&&(this.allowRedirects=!1));let d=new Bn(i,l,Object.freeze(m({},this.urlTree.queryParams)),this.urlTree.fragment,Cy(o),ut(o),o.component??o._loadedComponent??null,o,Ey(o)),p=Ha(d,a,this.paramsInheritanceStrategy);d.params=Object.freeze(p.params),d.data=Object.freeze(p.data);let v=this.applyRedirects.applyRedirectCommands(u,o.redirectTo,f,d,t);return this.applyRedirects.lineralizeSegments(o,v).pipe(se(E=>this.processSegment(t,r,n,E.concat(h),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=mA(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(Pe(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(Pe(({routes:l})=>{let u=r._loadedInjector??t,{parameters:f,consumedSegments:h,remainingSegments:d}=c,p=new Bn(h,f,Object.freeze(m({},this.urlTree.queryParams)),this.urlTree.fragment,Cy(r),ut(r),r.component??r._loadedComponent??null,r,Ey(r)),v=Ha(p,s,this.paramsInheritanceStrategy);p.params=Object.freeze(v.params),p.data=Object.freeze(v.data);let{segmentGroup:E,slicedSegments:L}=Dy(n,h,d,l);if(L.length===0&&E.hasChildren())return this.processChildren(u,l,E,p).pipe(I(J=>new Be(p,J)));if(l.length===0&&L.length===0)return b(new Be(p,[]));let zn=ut(r)===i;return this.processSegment(u,l,E,L,zn?N:i,!0,p).pipe(I(J=>new Be(p,J instanceof Be?[J]:[])))}))):Nr(n)))}getChildConfig(t,n,r){return n.children?b({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?b({routes:n._loadedRoutes,injector:n._loadedInjector}):fA(t,n,r,this.urlSerializer).pipe(se(o=>o?this.configLoader.loadChildren(t,n).pipe(ae(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):gA(n))):b({routes:[],injector:t})}};function IA(e){e.sort((t,n)=>t.value.outlet===N?-1:n.value.outlet===N?1:t.value.outlet.localeCompare(n.value.outlet))}function MA(e){let t=e.value.routeConfig;return t&&t.path===""}function Zy(e){let t=[],n=new Set;for(let r of e){if(!MA(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=Zy(r.children);t.push(new Be(r.value,o))}return t.filter(r=>!n.has(r))}function Cy(e){return e.data||{}}function Ey(e){return e.resolve||{}}function SA(e,t,n,r,o,i){return se(s=>_A(e,t,n,r,s.extractedUrl,o,i).pipe(I(({state:a,tree:c})=>O(m({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function TA(e,t){return se(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return b(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of Yy(c))s.add(l);let a=0;return K(s).pipe(Ht(c=>i.has(c)?AA(c,r,e,t):(c.data=Ha(c,c.parent,e).resolve,b(void 0))),ae(()=>a++),Xn(1),se(c=>a===s.size?b(n):Ee))})}function Yy(e){let t=e.children.map(n=>Yy(n)).flat();return[e,...t]}function AA(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!By(o)&&(i[Ko]=o.title),NA(i,e,t,r).pipe(I(s=>(e._resolvedData=s,e.data=Ha(e,e.parent,n).resolve,null)))}function NA(e,t,n,r){let o=Hd(e);if(o.length===0)return b({});let i={};return K(o).pipe(se(s=>xA(e[s],t,n,r).pipe(Tt(),ae(a=>{if(a instanceof Lr)throw za(new $n,a);i[s]=a}))),Xn(1),I(()=>i),_e(s=>Gy(s)?Ee:It(s)))}function xA(e,t,n,r){let o=Xo(t)??r,i=Br(e,o),s=i.resolve?i.resolve(t,n):Le(o,()=>i(t,n));return ln(s)}function Ud(e){return Pe(t=>{let n=e(t);return n?K(n).pipe(I(()=>t)):b(t)})}var sf=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===N);return r}getResolvedTitleForRoute(n){return n.data[Ko]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>g(Qy),providedIn:"root"})}return e})(),Qy=(()=>{class e extends sf{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(w(fy))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ni=new y("",{providedIn:"root",factory:()=>({})}),ri=new y(""),Ky=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=g(fv);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return b(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=ln(n.loadComponent()).pipe(I(Xy),ae(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),Gt(()=>{this.componentLoaders.delete(n)})),o=new Kn(r,()=>new W).pipe(Qn());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return b({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=Jy(r,this.compiler,n,this.onLoadEndListener).pipe(Gt(()=>{this.childrenLoaders.delete(r)})),s=new Kn(i,()=>new W).pipe(Qn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Jy(e,t,n,r){return ln(e.loadChildren()).pipe(I(Xy),se(o=>o instanceof sd||Array.isArray(o)?b(o):K(t.compileModuleAsync(o))),I(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(ri,[],{optional:!0,self:!0}).flat()),{routes:s.map(of),injector:i}}))}function RA(e){return e&&typeof e=="object"&&"default"in e}function Xy(e){return RA(e)?e.default:e}var Ya=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>g(OA),providedIn:"root"})}return e})(),OA=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),eD=new y("");var tD=new y(""),nD=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new W;transitionAbortSubject=new W;configLoader=g(Ky);environmentInjector=g(ye);destroyRef=g(vr);urlSerializer=g(Jo);rootContexts=g(jr);location=g(Mr);inputBindingEnabled=g(Wa,{optional:!0})!==null;titleStrategy=g(sf);options=g(ni,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=g(Ya);createViewTransition=g(eD,{optional:!0});navigationErrorHandler=g(tD,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>b(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new ka(o)),r=o=>this.events.next(new Fa(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(O(m({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(n){return this.transitions=new X(null),this.transitions.pipe(Oe(r=>r!==null),Pe(r=>{let o=!1,i=!1;return b(r).pipe(Pe(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",xe.SupersededByNewNavigation),Ee;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?O(m({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!a&&c!=="reload"){let l="";return this.events.next(new Bt(s.id,this.urlSerializer.serialize(s.rawUrl),l,Ho.IgnoredSameUrlNavigation)),s.resolve(!1),Ee}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return b(s).pipe(Pe(l=>(this.events.next(new Hn(l.id,this.urlSerializer.serialize(l.extractedUrl),l.source,l.restoredState)),l.id!==this.navigationId?Ee:Promise.resolve(l))),SA(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),ae(l=>{r.targetSnapshot=l.targetSnapshot,r.urlAfterRedirects=l.urlAfterRedirects,this.currentNavigation=O(m({},this.currentNavigation),{finalUrl:l.urlAfterRedirects});let u=new zo(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(u)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:l,extractedUrl:u,source:f,restoredState:h,extras:d}=s,p=new Hn(l,this.urlSerializer.serialize(u),f,h);this.events.next(p);let v=Vy(this.rootComponentType).snapshot;return this.currentTransition=r=O(m({},s),{targetSnapshot:v,urlAfterRedirects:u,extras:O(m({},d),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=u,b(r)}else{let l="";return this.events.next(new Bt(s.id,this.urlSerializer.serialize(s.extractedUrl),l,Ho.IgnoredByUrlHandlingStrategy)),s.resolve(!1),Ee}}),ae(s=>{let a=new xa(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),I(s=>(this.currentTransition=r=O(m({},s),{guards:WT(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),oA(this.environmentInjector,s=>this.events.next(s)),ae(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw za(this.urlSerializer,s.guardsResult);let a=new Ra(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),Oe(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",xe.GuardRejected),!1)),Ud(s=>{if(s.guards.canActivateChecks.length!==0)return b(s).pipe(ae(a=>{let c=new Oa(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),Pe(a=>{let c=!1;return b(a).pipe(TA(this.paramsInheritanceStrategy,this.environmentInjector),ae({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",xe.NoDataFromResolver)}}))}),ae(a=>{let c=new Pa(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),Ud(s=>{let a=c=>{let l=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&l.push(this.configLoader.loadComponent(c.routeConfig).pipe(ae(u=>{c.component=u}),I(()=>{})));for(let u of c.children)l.push(...a(u));return l};return Vi(a(s.targetSnapshot.root)).pipe(zt(null),St(1))}),Ud(()=>this.afterPreactivation()),Pe(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?K(c).pipe(I(()=>r)):b(r)}),I(s=>{let a=$T(n.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=O(m({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),ae(()=>{this.events.next(new Go)}),qT(this.rootContexts,n.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),St(1),ae({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new jt(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),er(this.transitionAbortSubject.pipe(ae(s=>{throw s}))),Gt(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",xe.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),_e(s=>{if(this.destroyed)return r.resolve(!1),Ee;if(i=!0,zy(s))this.events.next(new wt(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),GT(s)?this.events.next(new Fr(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new kr(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=Le(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof Lr){let{message:l,cancellationCode:u}=za(this.urlSerializer,c);this.events.next(new wt(r.id,this.urlSerializer.serialize(r.extractedUrl),l,u)),this.events.next(new Fr(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return Ee}))}))}cancelNavigationTransition(n,r,o){let i=new wt(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function PA(e){return e!==Ta}var rD=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>g(kA),providedIn:"root"})}return e})(),qa=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},kA=(()=>{class e extends qa{static \u0275fac=(()=>{let n;return function(o){return(n||(n=Rn(e)))(o||e)}})();static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),oD=(()=>{class e{urlSerializer=g(Jo);options=g(ni,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=g(Mr);urlHandlingStrategy=g(Ya);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new _t;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof _t?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=Vy(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>g(FA),providedIn:"root"})}return e})(),FA=(()=>{class e extends oD{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof Hn?this.updateStateMemento():n instanceof Bt?this.commitTransition(r):n instanceof zo?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Go?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof wt&&(n.code===xe.GuardRejected||n.code===xe.NoDataFromResolver)?this.restoreHistory(r):n instanceof kr?this.restoreHistory(r,!0):n instanceof jt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=m(m({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=m(m({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=Rn(e)))(o||e)}})();static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function af(e,t){e.events.pipe(Oe(n=>n instanceof jt||n instanceof wt||n instanceof kr||n instanceof Bt),I(n=>n instanceof jt||n instanceof Bt?0:(n instanceof wt?n.code===xe.Redirect||n.code===xe.SupersededByNewNavigation:!1)?2:1),Oe(n=>n!==2),St(1)).subscribe(()=>{t()})}var LA={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},VA={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Qa=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=g(ad);stateManager=g(oD);options=g(ni,{optional:!0})||{};pendingTasks=g(st);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=g(nD);urlSerializer=g(Jo);location=g(Mr);urlHandlingStrategy=g(Ya);_events=new W;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=g(rD);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=g(ri,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!g(Wa,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new ne;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof wt&&r.code!==xe.Redirect&&r.code!==xe.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof jt)this.navigated=!0;else if(r instanceof Fr){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=m({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||PA(o.source)},s);this.scheduleNavigation(a,Ta,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}BA(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Ta,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=m({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(of),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,u=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":u=m(m({},this.currentUrlTree.queryParams),i);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=i||null}u!==null&&(u=this.removeEmptyProps(u));let f;try{let h=o?o.snapshot:this.routerState.snapshot.root;f=Py(h)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),f=this.currentUrlTree.root}return ky(f,n,u,l??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=Pr(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Ta,null,r)}navigate(n,r={skipLocationChange:!1}){return jA(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=m({},LA):r===!1?o=m({},VA):o=r,Pr(n))return gy(this.currentUrlTree,n,o);let i=this.parseUrl(n);return gy(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((f,h)=>{a=f,c=h});let u=this.pendingTasks.add();return af(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(f=>Promise.reject(f))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function jA(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new D(4008,!1)}function BA(e){return!(e instanceof Go)&&!(e instanceof Fr)}var $A=new y("");function cf(e,...t){return Kt([{provide:ri,multi:!0,useValue:e},[],{provide:cn,useFactory:HA,deps:[Qa]},{provide:Pn,multi:!0,useFactory:zA},t.map(n=>n.\u0275providers)])}function HA(e){return e.routerState.root}function zA(){let e=g(Ge);return t=>{let n=e.get(Ae);if(t!==n.components[0])return;let r=e.get(Qa),o=e.get(GA);e.get(qA)===1&&r.initialNavigation(),e.get(WA,null,x.Optional)?.setUpPreloading(),e.get($A,null,x.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var GA=new y("",{factory:()=>new W}),qA=new y("",{providedIn:"root",factory:()=>1});var WA=new y("");var iD=[];var sD={providers:[pv({eventCoalescing:!0}),cf(iD),py(hy()),kd(Fd())]};var gD=(()=>{class e{_renderer;_elementRef;onChange=n=>{};onTouched=()=>{};constructor(n,r){this._renderer=n,this._elementRef=r}setProperty(n,r){this._renderer.setProperty(this._elementRef.nativeElement,n,r)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}static \u0275fac=function(r){return new(r||e)(B(wr),B(Ct))};static \u0275dir=De({type:e})}return e})(),hf=(()=>{class e extends gD{static \u0275fac=(()=>{let n;return function(o){return(n||(n=Rn(e)))(o||e)}})();static \u0275dir=De({type:e,features:[nn]})}return e})(),tc=new y("");var YA={provide:tc,useExisting:Tn(()=>nc),multi:!0};function QA(){let e=Qe()?Qe().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var KA=new y(""),nc=(()=>{class e extends gD{_compositionMode;_composing=!1;constructor(n,r,o){super(n,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!QA())}writeValue(n){let r=n??"";this.setProperty("value",r)}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}static \u0275fac=function(r){return new(r||e)(B(wr),B(Ct),B(KA,8))};static \u0275dir=De({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&Ce("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[wo([YA]),nn]})}return e})();var JA=new y(""),XA=new y("");function mD(e){return e!=null}function vD(e){return On(e)?K(e):e}function yD(e){let t={};return e.forEach(n=>{t=n!=null?m(m({},t),n):t}),Object.keys(t).length===0?null:t}function DD(e,t){return t.map(n=>n(e))}function eN(e){return!e.validate}function CD(e){return e.map(t=>eN(t)?t:n=>t.validate(n))}function tN(e){if(!e)return null;let t=e.filter(mD);return t.length==0?null:function(n){return yD(DD(n,t))}}function ED(e){return e!=null?tN(CD(e)):null}function nN(e){if(!e)return null;let t=e.filter(mD);return t.length==0?null:function(n){let r=DD(n,t).map(vD);return Vc(r).pipe(I(yD))}}function wD(e){return e!=null?nN(CD(e)):null}function aD(e,t){return e===null?[t]:Array.isArray(e)?[...e,t]:[e,t]}function rN(e){return e._rawValidators}function oN(e){return e._rawAsyncValidators}function lf(e){return e?Array.isArray(e)?e:[e]:[]}function Ja(e,t){return Array.isArray(e)?e.includes(t):e===t}function cD(e,t){let n=lf(t);return lf(e).forEach(o=>{Ja(n,o)||n.push(o)}),n}function lD(e,t){return lf(t).filter(n=>!Ja(e,n))}var Xa=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=ED(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=wD(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return this.control?this.control.hasError(t,n):!1}getError(t,n){return this.control?this.control.getError(t,n):null}},uf=class extends Xa{name;get formDirective(){return null}get path(){return null}},ci=class extends Xa{_parent=null;name=null;valueAccessor=null},df=class{_cd;constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},iN={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},_B=O(m({},iN),{"[class.ng-submitted]":"isSubmitted"}),_D=(()=>{class e extends df{constructor(n){super(n)}static \u0275fac=function(r){return new(r||e)(B(ci,2))};static \u0275dir=De({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&kt("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[nn]})}return e})();var oi="VALID",Ka="INVALID",Ur="PENDING",ii="DISABLED",Hr=class{},ec=class extends Hr{value;source;constructor(t,n){super(),this.value=t,this.source=n}},si=class extends Hr{pristine;source;constructor(t,n){super(),this.pristine=t,this.source=n}},ai=class extends Hr{touched;source;constructor(t,n){super(),this.touched=t,this.source=n}},$r=class extends Hr{status;source;constructor(t,n){super(),this.status=t,this.source=n}};function sN(e){return(rc(e)?e.validators:e)||null}function aN(e){return Array.isArray(e)?ED(e):e||null}function cN(e,t){return(rc(t)?t.asyncValidators:e)||null}function lN(e){return Array.isArray(e)?wD(e):e||null}function rc(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}var ff=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(t,n){this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return rn(this.statusReactive)}set status(t){rn(()=>this.statusReactive.set(t))}_status=bo(()=>this.statusReactive());statusReactive=vo(void 0);get valid(){return this.status===oi}get invalid(){return this.status===Ka}get pending(){return this.status==Ur}get disabled(){return this.status===ii}get enabled(){return this.status!==ii}errors;get pristine(){return rn(this.pristineReactive)}set pristine(t){rn(()=>this.pristineReactive.set(t))}_pristine=bo(()=>this.pristineReactive());pristineReactive=vo(!0);get dirty(){return!this.pristine}get touched(){return rn(this.touchedReactive)}set touched(t){rn(()=>this.touchedReactive.set(t))}_touched=bo(()=>this.touchedReactive());touchedReactive=vo(!1);get untouched(){return!this.touched}_events=new W;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(cD(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(cD(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(lD(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(lD(t,this._rawAsyncValidators))}hasValidator(t){return Ja(this._rawValidators,t)}hasAsyncValidator(t){return Ja(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let n=this.touched===!1;this.touched=!0;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(O(m({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new ai(!0,r))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(t))}markAsUntouched(t={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:r})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,r),n&&t.emitEvent!==!1&&this._events.next(new ai(!1,r))}markAsDirty(t={}){let n=this.pristine===!0;this.pristine=!1;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(O(m({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new si(!1,r))}markAsPristine(t={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,r),n&&t.emitEvent!==!1&&this._events.next(new si(!0,r))}markAsPending(t={}){this.status=Ur;let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new $r(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(O(m({},t),{sourceControl:n}))}disable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=ii,this.errors=null,this._forEachChild(o=>{o.disable(O(m({},t),{onlySelf:!0}))}),this._updateValue();let r=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new ec(this.value,r)),this._events.next(new $r(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(O(m({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=oi,this._forEachChild(r=>{r.enable(O(m({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(O(m({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t,n){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===oi||this.status===Ur)&&this._runAsyncValidator(r,t.emitEvent)}let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new ec(this.value,n)),this._events.next(new $r(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(O(m({},t),{sourceControl:n}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?ii:oi}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,n){if(this.asyncValidator){this.status=Ur,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1};let r=vD(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:n,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(t){let n=t;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((r,o)=>r&&r._find(o),this)}getError(t,n){let r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,n,r){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||r)&&this._events.next(new $r(this.status,n)),this._parent&&this._parent._updateControlsErrors(t,n,r)}_initObservables(){this.valueChanges=new de,this.statusChanges=new de}_calculateStatus(){return this._allControlsDisabled()?ii:this.errors?Ka:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Ur)?Ur:this._anyControlsHaveStatus(Ka)?Ka:oi}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,n){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),o&&this._events.next(new si(this.pristine,n))}_updateTouched(t={},n){this.touched=this._anyControlsTouched(),this._events.next(new ai(this.touched,n)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n)}_onDisabledChange=[];_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){rc(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let n=this._parent&&this._parent.dirty;return!t&&!!n&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=aN(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=lN(this._rawAsyncValidators)}};var bD=new y("",{providedIn:"root",factory:()=>pf}),pf="always";function uN(e,t){return[...t.path,e]}function dN(e,t,n=pf){hN(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||n==="always")&&t.valueAccessor.setDisabledState?.(e.disabled),pN(e,t),mN(e,t),gN(e,t),fN(e,t)}function uD(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function fN(e,t){if(t.valueAccessor.setDisabledState){let n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}function hN(e,t){let n=rN(e);t.validator!==null?e.setValidators(aD(n,t.validator)):typeof n=="function"&&e.setValidators([n]);let r=oN(e);t.asyncValidator!==null?e.setAsyncValidators(aD(r,t.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();uD(t._rawValidators,o),uD(t._rawAsyncValidators,o)}function pN(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&ID(e,t)})}function gN(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&ID(e,t),e.updateOn!=="submit"&&e.markAsTouched()})}function ID(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function mN(e,t){let n=(r,o)=>{t.valueAccessor.writeValue(r),o&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}function vN(e,t){if(!e.hasOwnProperty("model"))return!1;let n=e.model;return n.isFirstChange()?!0:!Object.is(t,n.currentValue)}function yN(e){return Object.getPrototypeOf(e.constructor)===hf}function DN(e,t){if(!t)return null;Array.isArray(t);let n,r,o;return t.forEach(i=>{i.constructor===nc?n=i:yN(i)?r=i:o=i}),o||r||n||null}function dD(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function fD(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var CN=class extends ff{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(t=null,n,r){super(sN(n),cN(r,n)),this._applyFormState(t),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),rc(n)&&(n.nonNullable||n.initialValueIsDefault)&&(fD(t)?this.defaultValue=t.value:this.defaultValue=t)}setValue(t,n={}){this.value=this._pendingValue=t,this._onChange.length&&n.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,n.emitViewToModelChange!==!1)),this.updateValueAndValidity(n)}patchValue(t,n={}){this.setValue(t,n)}reset(t=this.defaultValue,n={}){this._applyFormState(t),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){dD(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){dD(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(t){fD(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}};var EN={provide:ci,useExisting:Tn(()=>gf)},hD=Promise.resolve(),gf=(()=>{class e extends ci{_changeDetectorRef;callSetDisabledState;control=new CN;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new de;constructor(n,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this._parent=n,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=DN(this,i)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){let r=n.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),vN(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){dN(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(n){hD.then(()=>{this.control.setValue(n,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(n){let r=n.isDisabled.currentValue,o=r!==0&&hd(r);hD.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(n){return this._parent?uN(n,this._parent):[n]}static \u0275fac=function(r){return new(r||e)(B(uf,9),B(JA,10),B(XA,10),B(tc,10),B(_o,8),B(bD,8))};static \u0275dir=De({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[wo([EN]),nn,ho]})}return e})();var wN={provide:tc,useExisting:Tn(()=>oc),multi:!0};function MD(e,t){return e==null?`${t}`:(t&&typeof t=="object"&&(t="Object"),`${e}: ${t}`.slice(0,50))}function _N(e){return e.split(":")[0]}var oc=(()=>{class e extends hf{value;_optionMap=new Map;_idCounter=0;set compareWith(n){this._compareWith=n}_compareWith=Object.is;writeValue(n){this.value=n;let r=this._getOptionId(n),o=MD(r,n);this.setProperty("value",o)}registerOnChange(n){this.onChange=r=>{this.value=this._getOptionValue(r),n(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(n){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r),n))return r;return null}_getOptionValue(n){let r=_N(n);return this._optionMap.has(r)?this._optionMap.get(r):n}static \u0275fac=(()=>{let n;return function(o){return(n||(n=Rn(e)))(o||e)}})();static \u0275dir=De({type:e,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(r,o){r&1&&Ce("change",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[wo([wN]),nn]})}return e})(),SD=(()=>{class e{_element;_renderer;_select;id;constructor(n,r,o){this._element=n,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption())}set ngValue(n){this._select!=null&&(this._select._optionMap.set(this.id,n),this._setElementValue(MD(this.id,n)),this._select.writeValue(this._select.value))}set value(n){this._setElementValue(n),this._select&&this._select.writeValue(this._select.value)}_setElementValue(n){this._renderer.setProperty(this._element.nativeElement,"value",n)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(r){return new(r||e)(B(Ct),B(wr),B(oc,9))};static \u0275dir=De({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})(),bN={provide:tc,useExisting:Tn(()=>TD),multi:!0};function pD(e,t){return e==null?`${t}`:(typeof t=="string"&&(t=`'${t}'`),t&&typeof t=="object"&&(t="Object"),`${e}: ${t}`.slice(0,50))}function IN(e){return e.split(":")[0]}var TD=(()=>{class e extends hf{value;_optionMap=new Map;_idCounter=0;set compareWith(n){this._compareWith=n}_compareWith=Object.is;writeValue(n){this.value=n;let r;if(Array.isArray(n)){let o=n.map(i=>this._getOptionId(i));r=(i,s)=>{i._setSelected(o.indexOf(s.toString())>-1)}}else r=(o,i)=>{o._setSelected(!1)};this._optionMap.forEach(r)}registerOnChange(n){this.onChange=r=>{let o=[],i=r.selectedOptions;if(i!==void 0){let s=i;for(let a=0;a<s.length;a++){let c=s[a],l=this._getOptionValue(c.value);o.push(l)}}else{let s=r.options;for(let a=0;a<s.length;a++){let c=s[a];if(c.selected){let l=this._getOptionValue(c.value);o.push(l)}}}this.value=o,n(o)}}_registerOption(n){let r=(this._idCounter++).toString();return this._optionMap.set(r,n),r}_getOptionId(n){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r)._value,n))return r;return null}_getOptionValue(n){let r=IN(n);return this._optionMap.has(r)?this._optionMap.get(r)._value:n}static \u0275fac=(()=>{let n;return function(o){return(n||(n=Rn(e)))(o||e)}})();static \u0275dir=De({type:e,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(r,o){r&1&&Ce("change",function(s){return o.onChange(s.target)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[wo([bN]),nn]})}return e})(),AD=(()=>{class e{_element;_renderer;_select;id;_value;constructor(n,r,o){this._element=n,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption(this))}set ngValue(n){this._select!=null&&(this._value=n,this._setElementValue(pD(this.id,n)),this._select.writeValue(this._select.value))}set value(n){this._select?(this._value=n,this._setElementValue(pD(this.id,n)),this._select.writeValue(this._select.value)):this._setElementValue(n)}_setElementValue(n){this._renderer.setProperty(this._element.nativeElement,"value",n)}_setSelected(n){this._renderer.setProperty(this._element.nativeElement,"selected",n)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(r){return new(r||e)(B(Ct),B(wr),B(TD,9))};static \u0275dir=De({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})();var MN=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=tn({type:e});static \u0275inj=Qt({})}return e})();var ND=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:bD,useValue:n.callSetDisabledState??pf}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=tn({type:e});static \u0275inj=Qt({imports:[MN]})}return e})();var ic=class e{constructor(t,n){this.http=t;this.platformId=n}config={baseUrl:"http://localhost:47334",database:"mindsdb"};isConnectedSubject=new X(!1);isConnected$=this.isConnectedSubject.asObservable();httpOptions={headers:new Je({"Content-Type":"application/json",Accept:"application/json"})};configure(t){this.config=m(m({},this.config),t),this.updateHttpOptions()}testConnection(){return sn(this.platformId)?this.http.get(`${this.config.baseUrl}/api/status`,this.httpOptions).pipe(I(()=>(this.isConnectedSubject.next(!0),!0)),_e(t=>(console.error("MindsDB connection failed:",t),this.isConnectedSubject.next(!1),It(()=>t)))):(this.isConnectedSubject.next(!1),b(!1))}executeQuery(t){let n={query:t,context:{database:this.config.database}};return this.http.post(`${this.config.baseUrl}/api/sql/query`,n,this.httpOptions).pipe(I(r=>({data:r.data||[],columns:r.column_names||[],error:r.error_message})),_e(r=>(console.error("Query execution failed:",r),It(()=>r))))}getModels(){return this.executeQuery("SELECT name FROM mindsdb.models WHERE active = 1").pipe(I(n=>n.data.map(r=>r.name||r[0])))}createModel(t,n){let r=`
      CREATE MODEL ${t}
      PREDICT target
      USING
        engine = '${n.engine||"openai"}',
        ${Object.entries(n.parameters||{}).map(([o,i])=>`${o} = '${i}'`).join(`,
        `)}
    `;return this.executeQuery(r)}queryModel(t,n,r){let o=r?JSON.stringify(r):"{}",i=`
      SELECT response
      FROM ${t}
      WHERE text = '${n.replace(/'/g,"''")}'
      AND context = '${o}'
    `;return this.executeQuery(i).pipe(I(s=>{if(s.error)throw new Error(s.error);return s.data[0]?.response||s.data[0]?.[0]||"No response received"}))}getChatHistory(t){let n="SELECT * FROM chat_history ORDER BY timestamp DESC LIMIT 50";return t&&(n=`SELECT * FROM chat_history WHERE conversation_id = '${t}' ORDER BY timestamp DESC LIMIT 50`),this.executeQuery(n).pipe(I(r=>r.data.map(o=>({id:o.id||o[0],content:o.content||o[1],role:o.role||o[2],timestamp:new Date(o.timestamp||o[3])}))),_e(()=>[]))}saveChatMessage(t,n){let r=`
      INSERT INTO chat_history (id, content, role, timestamp, conversation_id)
      VALUES ('${t.id}', '${t.content.replace(/'/g,"''")}', '${t.role}', 
              '${t.timestamp.toISOString()}', '${n||"default"}')
    `;return this.executeQuery(r)}updateHttpOptions(){let t={"Content-Type":"application/json",Accept:"application/json"};if(this.config.username&&this.config.password){let n=btoa(`${this.config.username}:${this.config.password}`);t.Authorization=`Basic ${n}`}this.httpOptions={headers:new Je(t)}}static \u0275fac=function(n){return new(n||e)(w(Ea),w(at))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})};var sc=class e{constructor(t,n){this.ngZone=t;this.platformId=n}eventSource=null;eventsSubject=new W;connectionStatusSubject=new X({connected:!1});events$=this.eventsSubject.asObservable();connectionStatus$=this.connectionStatusSubject.asObservable();connect(t,n){if(!sn(this.platformId))return console.warn("SSE not available in server environment"),this.connectionStatusSubject.next({connected:!1,error:"SSE not available in server environment"}),this.events$;this.disconnect();try{this.eventSource=new EventSource(t,n),this.eventSource.onopen=r=>{this.ngZone.run(()=>{console.log("SSE connection opened:",r),this.connectionStatusSubject.next({connected:!0,lastEventTime:new Date})})},this.eventSource.onmessage=r=>{this.ngZone.run(()=>{try{let i={type:"message",data:r.data?JSON.parse(r.data):null,id:r.lastEventId};this.eventsSubject.next(i),this.connectionStatusSubject.next({connected:!0,lastEventTime:new Date})}catch(o){console.error("Error parsing SSE message:",o),this.eventsSubject.next({type:"error",data:{error:"Failed to parse message",raw:r.data}})}})},this.eventSource.onerror=r=>{this.ngZone.run(()=>{console.error("SSE connection error:",r);let o=this.eventSource?.readyState===EventSource.CLOSED?"Connection closed":"Connection error";this.connectionStatusSubject.next({connected:!1,error:o}),this.eventsSubject.next({type:"error",data:{error:o,event:r}})})},this.setupCustomEventListeners()}catch(r){console.error("Failed to create SSE connection:",r),this.connectionStatusSubject.next({connected:!1,error:"Failed to create connection"})}return this.events$}disconnect(){this.eventSource&&(this.eventSource.close(),this.eventSource=null,this.connectionStatusSubject.next({connected:!1}),console.log("SSE connection closed"))}isConnected(){return this.eventSource?.readyState===EventSource.OPEN}getReadyState(){return this.eventSource?.readyState||null}setupCustomEventListeners(){if(!this.eventSource)return;["chat-message","chat-response","chat-error","model-update","query-result","connection-status"].forEach(n=>{this.eventSource.addEventListener(n,r=>{this.ngZone.run(()=>{try{let o=r.data?JSON.parse(r.data):null,i={type:n,data:o,id:r.lastEventId};this.eventsSubject.next(i),this.connectionStatusSubject.next({connected:!0,lastEventTime:new Date})}catch(o){console.error(`Error parsing ${n} event:`,o),this.eventsSubject.next({type:"error",data:{error:`Failed to parse ${n}`,raw:r.data}})}})})})}getEventsByType(t){return new V(n=>{let r=this.events$.subscribe(o=>{o.type===t&&n.next(o)});return()=>r.unsubscribe()})}reconnect(t,n,r=5){let o=0,i=()=>{if(o>=r){console.error("Max reconnection attempts reached"),this.connectionStatusSubject.next({connected:!1,error:"Max reconnection attempts reached"});return}let s=Math.pow(2,o)*1e3;console.log(`Attempting to reconnect in ${s}ms (attempt ${o+1}/${r})`),setTimeout(()=>{o++,this.connect(t,n).subscribe({error:()=>{o<r&&i()}})},s)};i()}ngOnDestroy(){this.disconnect(),this.eventsSubject.complete(),this.connectionStatusSubject.complete()}static \u0275fac=function(n){return new(n||e)(w(ee),w(at))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})};var ac=class e{constructor(t,n,r){this.mindsdbService=t;this.sseService=n;this.platformId=r;this.initializeConnections(),sn(this.platformId)&&this.setupSSEListeners()}initialState={currentConversation:null,conversations:[],isLoading:!1,error:null,isConnected:!1};stateSubject=new X(this.initialState);state$=this.stateSubject.asObservable();messageSubject=new W;newMessage$=this.messageSubject.asObservable();selectedModel="gpt-4";sseUrl="http://localhost:47334/api/sse/chat";initializeConnections(){this.mindsdbService.isConnected$.subscribe(t=>{this.updateState({isConnected:t})}),sn(this.platformId)?this.mindsdbService.testConnection().subscribe({next:()=>{console.log("MindsDB connection established"),this.loadConversations()},error:t=>{console.error("Failed to connect to MindsDB:",t),this.updateState({error:"Failed to connect to MindsDB"})}}):this.createConversation()}setupSSEListeners(){this.sseService.connect(this.sseUrl).subscribe({next:t=>{this.handleSSEEvent(t)},error:t=>{console.error("SSE connection error:",t),this.updateState({error:"Real-time connection failed"})}}),this.sseService.connectionStatus$.subscribe(t=>{!t.connected&&t.error&&(console.warn("SSE disconnected:",t.error),setTimeout(()=>{this.sseService.reconnect(this.sseUrl)},5e3))})}handleSSEEvent(t){switch(t.type){case"chat-response":this.handleChatResponse(t.data);break;case"chat-error":this.updateState({error:t.data.message,isLoading:!1});break;case"model-update":console.log("Model updated:",t.data);break;default:console.log("Unhandled SSE event:",t)}}handleChatResponse(t){let n={id:this.generateMessageId(),content:t.response||t.content,role:"assistant",timestamp:new Date};this.addMessageToCurrentConversation(n),this.messageSubject.next(n),this.updateState({isLoading:!1})}sendMessage(t){let n={id:this.generateMessageId(),content:t,role:"user",timestamp:new Date};return this.addMessageToCurrentConversation(n),this.messageSubject.next(n),this.updateState({isLoading:!0,error:null}),this.mindsdbService.queryModel(this.selectedModel,t).pipe(I(r=>{let o={id:this.generateMessageId(),content:r,role:"assistant",timestamp:new Date};return this.addMessageToCurrentConversation(o),this.messageSubject.next(o),this.updateState({isLoading:!1}),o}),_e(r=>{throw console.error("Error sending message:",r),this.updateState({error:"Failed to send message: "+r.message,isLoading:!1}),r}))}createConversation(t){let n={id:this.generateConversationId(),title:t||`Chat ${new Date().toLocaleString()}`,messages:[],createdAt:new Date,updatedAt:new Date},r=this.stateSubject.value,o=[n,...r.conversations];return this.updateState({currentConversation:n,conversations:o}),n}switchConversation(t){let r=this.stateSubject.value.conversations.find(o=>o.id===t);r&&this.updateState({currentConversation:r})}deleteConversation(t){let n=this.stateSubject.value,r=n.conversations.filter(i=>i.id!==t),o=n.currentConversation;n.currentConversation?.id===t&&(o=r[0]||null),this.updateState({conversations:r,currentConversation:o})}setModel(t){this.selectedModel=t}getAvailableModels(){return this.mindsdbService.getModels()}clearCurrentConversation(){let t=this.stateSubject.value;if(t.currentConversation){let n=O(m({},t.currentConversation),{messages:[],updatedAt:new Date});this.updateState({currentConversation:n})}}addMessageToCurrentConversation(t){let n=this.stateSubject.value;n.currentConversation||this.createConversation();let r=O(m({},n.currentConversation),{messages:[...n.currentConversation.messages,t],updatedAt:new Date}),o=n.conversations.map(i=>i.id===r.id?r:i);this.updateState({currentConversation:r,conversations:o}),this.mindsdbService.saveChatMessage(t,r.id).subscribe({error:i=>console.warn("Failed to save message to MindsDB:",i)})}loadConversations(){this.mindsdbService.getChatHistory().subscribe({next:t=>{if(t.length>0){let n=this.createConversation("Loaded Chat");n.messages=t.reverse(),this.updateState({currentConversation:n})}},error:t=>{console.warn("Failed to load chat history:",t),this.createConversation()}})}updateState(t){let n=this.stateSubject.value;this.stateSubject.next(m(m({},n),t))}generateMessageId(){return`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}generateConversationId(){return`conv_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}static \u0275fac=function(n){return new(n||e)(w(ic),w(sc),w(at))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})};var RN=["messagesContainer"],ON=["messageInput"];function PN(e,t){if(e&1&&(A(0,"option",37),Z(1),P()),e&2){let n=t.$implicit;Ze("value",n),q(),Ir(" ",n," ")}}function kN(e,t){if(e&1){let n=Ys();A(0,"div",38),Ce("click",function(){let o=qe(n).$implicit,i=kn();return We(i.switchConversation(o.id))}),A(1,"div",39)(2,"div",40),Z(3),P(),A(4,"div",41),Z(5),P(),A(6,"div",42),Z(7),uv(8,"date"),P()(),A(9,"button",43),Ce("click",function(o){let i=qe(n).$implicit,s=kn();return We(s.deleteConversation(i.id,o))}),fe(10,"i",44),P()()}if(e&2){let n=t.$implicit,r=kn();kt("active",(r.chatState.currentConversation==null?null:r.chatState.currentConversation.id)===n.id),q(3),ct(n.title),q(2),ct(r.getConversationPreview(n)),q(2),ct(dv(8,5,n.updatedAt,"short"))}}function FN(e,t){if(e&1){let n=Ys();A(0,"div",45),fe(1,"i",46),A(2,"span"),Z(3),P(),A(4,"button",47),Ce("click",function(){qe(n);let o=kn();return We(o.retryLastMessage())}),Z(5,"Retry"),P()()}if(e&2){let n=kn();q(3),ct(n.chatState.error)}}function LN(e,t){e&1&&(A(0,"div",48)(1,"div",49)(2,"h2"),Z(3,"Welcome to MindsDB Chat"),P(),A(4,"p"),Z(5,"Start a conversation with your AI assistant powered by MindsDB."),P(),A(6,"div",50)(7,"div",51),fe(8,"i",52),A(9,"span"),Z(10,"AI-powered responses"),P()(),A(11,"div",51),fe(12,"i",53),A(13,"span"),Z(14,"Connected to MindsDB"),P()(),A(15,"div",51),fe(16,"i",54),A(17,"span"),Z(18,"Real-time communication"),P()()()()())}function VN(e,t){if(e&1&&(A(0,"div",55)(1,"div",56),fe(2,"i"),P(),A(3,"div",57)(4,"div",58)(5,"span",59),Z(6),P(),A(7,"span",60),Z(8),P()(),A(9,"div",61),Z(10),P()()()),e&2){let n=t.$implicit,r=kn();kt("user-message",r.isUserMessage(n))("assistant-message",!r.isUserMessage(n)),q(2),tv(r.isUserMessage(n)?"icon-user":"icon-bot"),q(4),ct(r.isUserMessage(n)?"You":"Assistant"),q(2),ct(r.getMessageTime(n)),q(2),ct(n.content)}}function jN(e,t){e&1&&(A(0,"div",62)(1,"div",56),fe(2,"i",63),P(),A(3,"div",57)(4,"div",58)(5,"span",59),Z(6,"Assistant"),P()(),A(7,"div",61)(8,"div",64),fe(9,"span")(10,"span")(11,"span"),P()()()())}var cc=class e{constructor(t){this.chatService=t}messagesContainer;messageInput;chatState={currentConversation:null,conversations:[],isLoading:!1,error:null,isConnected:!1};currentMessage="";availableModels=[];selectedModel="gpt-4";showSidebar=!0;destroy$=new W;shouldScrollToBottom=!1;ngOnInit(){this.chatService.state$.pipe(er(this.destroy$)).subscribe(t=>{this.chatState=t,this.shouldScrollToBottom=!0}),this.chatService.newMessage$.pipe(er(this.destroy$)).subscribe(()=>{this.shouldScrollToBottom=!0}),this.loadAvailableModels(),this.chatState.currentConversation||this.createNewConversation()}ngAfterViewChecked(){this.shouldScrollToBottom&&(this.scrollToBottom(),this.shouldScrollToBottom=!1)}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}sendMessage(){if(!this.currentMessage.trim()||this.chatState.isLoading)return;let t=this.currentMessage.trim();this.currentMessage="",this.chatService.sendMessage(t).subscribe({next:()=>{this.focusInput()},error:n=>{console.error("Failed to send message:",n),this.currentMessage=t}})}onKeyPress(t){t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),this.sendMessage())}createNewConversation(){this.chatService.createConversation(),this.focusInput()}switchConversation(t){this.chatService.switchConversation(t),this.focusInput()}deleteConversation(t,n){n.stopPropagation(),confirm("Are you sure you want to delete this conversation?")&&this.chatService.deleteConversation(t)}clearConversation(){confirm("Are you sure you want to clear this conversation?")&&this.chatService.clearCurrentConversation()}onModelChange(){this.chatService.setModel(this.selectedModel)}toggleSidebar(){this.showSidebar=!this.showSidebar}getMessageTime(t){return t.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}getConversationPreview(t){if(t.messages.length===0)return"New conversation";let n=t.messages[t.messages.length-1];return n.content.substring(0,50)+(n.content.length>50?"...":"")}isUserMessage(t){return t.role==="user"}retryLastMessage(){let t=this.chatState.currentConversation;if(t&&t.messages.length>0){let n=[...t.messages].reverse().find(r=>r.role==="user");n&&(this.currentMessage=n.content,this.focusInput())}}loadAvailableModels(){this.chatService.getAvailableModels().subscribe({next:t=>{this.availableModels=t,t.length>0&&!t.includes(this.selectedModel)&&(this.selectedModel=t[0],this.onModelChange())},error:t=>{console.error("Failed to load models:",t),this.availableModels=["gpt-4","gpt-3.5-turbo","claude-3"]}})}scrollToBottom(){if(this.messagesContainer){let t=this.messagesContainer.nativeElement;t.scrollTop=t.scrollHeight}}focusInput(){setTimeout(()=>{this.messageInput&&this.messageInput.nativeElement.focus()},100)}static \u0275fac=function(n){return new(n||e)(B(ac))};static \u0275cmp=_r({type:e,selectors:[["app-chat"]],viewQuery:function(n,r){if(n&1&&(cd(RN,5),cd(ON,5)),n&2){let o;ld(o=ud())&&(r.messagesContainer=o.first),ld(o=ud())&&(r.messageInput=o.first)}},decls:51,vars:21,consts:[["messagesContainer",""],["messageInput",""],[1,"chat-container"],[1,"sidebar"],[1,"sidebar-header"],[1,"btn","btn-primary",3,"click"],[1,"icon-plus"],[1,"connection-status"],[1,"status-indicator"],[1,"model-selection"],["for","model-select"],["id","model-select",1,"model-select",3,"ngModelChange","change","ngModel"],[3,"value",4,"ngFor","ngForOf"],[1,"conversations-list"],[1,"conversation-items"],["class","conversation-item",3,"active","click",4,"ngFor","ngForOf"],[1,"chat-main"],[1,"chat-header"],[1,"sidebar-toggle",3,"click"],[1,"icon-menu"],[1,"chat-title"],[1,"model-indicator"],[1,"chat-actions"],[1,"btn","btn-secondary",3,"click","disabled"],["class","error-banner",4,"ngIf"],[1,"messages-container"],[1,"messages-list"],["class","welcome-message",4,"ngIf"],["class","message",3,"user-message","assistant-message",4,"ngFor","ngForOf"],["class","message assistant-message loading",4,"ngIf"],[1,"message-input-container"],[1,"message-input-wrapper"],["placeholder","Type your message here... (Press Enter to send, Shift+Enter for new line)","rows","1",1,"message-input",3,"ngModelChange","keydown","ngModel","disabled"],[1,"send-button",3,"click","disabled"],[1,"icon-send"],[1,"input-footer"],[1,"input-hint"],[3,"value"],[1,"conversation-item",3,"click"],[1,"conversation-content"],[1,"conversation-title"],[1,"conversation-preview"],[1,"conversation-time"],["title","Delete conversation",1,"delete-btn",3,"click"],[1,"icon-trash"],[1,"error-banner"],[1,"icon-alert"],[1,"retry-btn",3,"click"],[1,"welcome-message"],[1,"welcome-content"],[1,"welcome-features"],[1,"feature"],[1,"icon-brain"],[1,"icon-database"],[1,"icon-realtime"],[1,"message"],[1,"message-avatar"],[1,"message-content"],[1,"message-header"],[1,"message-role"],[1,"message-time"],[1,"message-text"],[1,"message","assistant-message","loading"],[1,"icon-bot"],[1,"typing-indicator"]],template:function(n,r){if(n&1){let o=Ys();A(0,"div",2)(1,"div",3)(2,"div",4)(3,"h2"),Z(4,"MindsDB Chat"),P(),A(5,"button",5),Ce("click",function(){return qe(o),We(r.createNewConversation())}),fe(6,"i",6),Z(7," New Chat "),P()(),A(8,"div",7),fe(9,"div",8),A(10,"span"),Z(11),P()(),A(12,"div",9)(13,"label",10),Z(14,"Model:"),P(),A(15,"select",11),Ks("ngModelChange",function(s){return qe(o),dd(r.selectedModel,s)||(r.selectedModel=s),We(s)}),Ce("change",function(){return qe(o),We(r.onModelChange())}),br(16,PN,2,2,"option",12),P()(),A(17,"div",13)(18,"h3"),Z(19,"Conversations"),P(),A(20,"div",14),br(21,kN,11,8,"div",15),P()()(),A(22,"div",16)(23,"div",17)(24,"button",18),Ce("click",function(){return qe(o),We(r.toggleSidebar())}),fe(25,"i",19),P(),A(26,"div",20)(27,"h1"),Z(28),P(),A(29,"span",21),Z(30),P()(),A(31,"div",22)(32,"button",23),Ce("click",function(){return qe(o),We(r.clearConversation())}),Z(33," Clear Chat "),P()()(),br(34,FN,6,1,"div",24),A(35,"div",25,0)(37,"div",26),br(38,LN,19,0,"div",27)(39,VN,11,9,"div",28)(40,jN,12,0,"div",29),P()(),A(41,"div",30)(42,"div",31)(43,"textarea",32,1),Ks("ngModelChange",function(s){return qe(o),dd(r.currentMessage,s)||(r.currentMessage=s),We(s)}),Ce("keydown",function(s){return qe(o),We(r.onKeyPress(s))}),Z(45,"        "),P(),A(46,"button",33),Ce("click",function(){return qe(o),We(r.sendMessage())}),fe(47,"i",34),P()(),A(48,"div",35)(49,"span",36),Z(50),P()()()()()}n&2&&(q(),kt("hidden",!r.showSidebar),q(7),kt("connected",r.chatState.isConnected),q(3),ct(r.chatState.isConnected?"Connected":"Disconnected"),q(4),Qs("ngModel",r.selectedModel),q(),Ze("ngForOf",r.availableModels),q(5),Ze("ngForOf",r.chatState.conversations),q(),kt("full-width",!r.showSidebar),q(6),ct((r.chatState.currentConversation==null?null:r.chatState.currentConversation.title)||"MindsDB Chat"),q(2),Ir("Using: ",r.selectedModel,""),q(2),Ze("disabled",!(!(r.chatState.currentConversation==null||r.chatState.currentConversation.messages==null)&&r.chatState.currentConversation.messages.length)),q(2),Ze("ngIf",r.chatState.error),q(4),Ze("ngIf",!(!(r.chatState.currentConversation==null||r.chatState.currentConversation.messages==null)&&r.chatState.currentConversation.messages.length)),q(),Ze("ngForOf",r.chatState.currentConversation==null?null:r.chatState.currentConversation.messages),q(),Ze("ngIf",r.chatState.isLoading),q(3),Qs("ngModel",r.currentMessage),Ze("disabled",r.chatState.isLoading||!r.chatState.isConnected),q(3),Ze("disabled",!r.currentMessage.trim()||r.chatState.isLoading||!r.chatState.isConnected),q(4),Ir(" ",r.chatState.isConnected?"Connected to MindsDB":"Connecting..."," "))},dependencies:[la,ca,Dd,Cd,ND,SD,AD,nc,oc,_D,gf],styles:['@charset "UTF-8";.chat-container[_ngcontent-%COMP%]{display:flex;height:100vh;background:#f5f5f5;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif}.sidebar[_ngcontent-%COMP%]{width:300px;background:#2c3e50;color:#fff;display:flex;flex-direction:column;transition:transform .3s ease}.sidebar.hidden[_ngcontent-%COMP%]{transform:translate(-100%);width:0}.sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]{padding:20px;border-bottom:1px solid #34495e}.sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 15px;font-size:1.5rem;font-weight:600}.sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%;padding:10px;border:none;border-radius:6px;cursor:pointer;font-weight:500;display:flex;align-items:center;justify-content:center;gap:8px}.sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{background:#3498db;color:#fff}.sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover{background:#2980b9}.sidebar[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]{padding:15px 20px;display:flex;align-items:center;gap:10px;border-bottom:1px solid #34495e}.sidebar[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background:#e74c3c}.sidebar[_ngcontent-%COMP%]   .connection-status.connected[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{background:#27ae60}.sidebar[_ngcontent-%COMP%]   .model-selection[_ngcontent-%COMP%]{padding:15px 20px;border-bottom:1px solid #34495e}.sidebar[_ngcontent-%COMP%]   .model-selection[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:8px;font-weight:500}.sidebar[_ngcontent-%COMP%]   .model-selection[_ngcontent-%COMP%]   .model-select[_ngcontent-%COMP%]{width:100%;padding:8px;border:1px solid #34495e;border-radius:4px;background:#34495e;color:#fff}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{padding:15px 20px 10px;margin:0;font-size:1rem;font-weight:600;border-bottom:1px solid #34495e}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   .conversation-items[_ngcontent-%COMP%]{padding:10px 0}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   .conversation-item[_ngcontent-%COMP%]{padding:12px 20px;cursor:pointer;border-bottom:1px solid #34495e;display:flex;align-items:center;justify-content:space-between;transition:background .2s}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   .conversation-item[_ngcontent-%COMP%]:hover{background:#34495e}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   .conversation-item.active[_ngcontent-%COMP%]{background:#3498db}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   .conversation-item[_ngcontent-%COMP%]   .conversation-content[_ngcontent-%COMP%]{flex:1;min-width:0}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   .conversation-item[_ngcontent-%COMP%]   .conversation-content[_ngcontent-%COMP%]   .conversation-title[_ngcontent-%COMP%]{font-weight:500;margin-bottom:4px}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   .conversation-item[_ngcontent-%COMP%]   .conversation-content[_ngcontent-%COMP%]   .conversation-preview[_ngcontent-%COMP%]{font-size:.85rem;opacity:.7;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   .conversation-item[_ngcontent-%COMP%]   .conversation-content[_ngcontent-%COMP%]   .conversation-time[_ngcontent-%COMP%]{font-size:.75rem;opacity:.5;margin-top:4px}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   .conversation-item[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]{background:none;border:none;color:#e74c3c;cursor:pointer;padding:4px;border-radius:4px;opacity:0;transition:opacity .2s}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   .conversation-item[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover{background:#e74c3c;color:#fff}.sidebar[_ngcontent-%COMP%]   .conversations-list[_ngcontent-%COMP%]   .conversation-item[_ngcontent-%COMP%]:hover   .delete-btn[_ngcontent-%COMP%]{opacity:1}.chat-main[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;background:#fff}.chat-main.full-width[_ngcontent-%COMP%]{width:100%}.chat-main[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]{padding:15px 20px;border-bottom:1px solid #e1e8ed;display:flex;align-items:center;gap:15px;background:#fff}.chat-main[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .sidebar-toggle[_ngcontent-%COMP%]{background:none;border:none;font-size:1.2rem;cursor:pointer;padding:8px;border-radius:4px}.chat-main[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .sidebar-toggle[_ngcontent-%COMP%]:hover{background:#f8f9fa}.chat-main[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-title[_ngcontent-%COMP%]{flex:1}.chat-main[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:1.3rem;font-weight:600}.chat-main[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-title[_ngcontent-%COMP%]   .model-indicator[_ngcontent-%COMP%]{font-size:.85rem;color:#666}.chat-main[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:8px 16px;border:1px solid #ddd;border-radius:4px;background:#fff;cursor:pointer}.chat-main[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#f8f9fa}.chat-main[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .chat-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.chat-main[_ngcontent-%COMP%]   .error-banner[_ngcontent-%COMP%]{background:#fee;color:#c33;padding:12px 20px;display:flex;align-items:center;gap:10px;border-bottom:1px solid #fcc}.chat-main[_ngcontent-%COMP%]   .error-banner[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:#c33;color:#fff;border:none;padding:4px 12px;border-radius:4px;cursor:pointer;font-size:.85rem}.chat-main[_ngcontent-%COMP%]   .error-banner[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{background:#a22}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:20px}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]{max-width:500px;margin:0 auto}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin-bottom:15px;color:#2c3e50}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:30px}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .welcome-features[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:30px;flex-wrap:wrap}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   .welcome-content[_ngcontent-%COMP%]   .welcome-features[_ngcontent-%COMP%]   .feature[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;color:#3498db;font-weight:500}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{display:flex;gap:12px;margin-bottom:20px}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .message.user-message[_ngcontent-%COMP%]{flex-direction:row-reverse}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:#3498db;color:#fff}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .message.assistant-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:#f8f9fa;border:1px solid #e1e8ed}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-avatar[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:50%;background:#ddd;display:flex;align-items:center;justify-content:center;flex-shrink:0}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:70%;padding:12px 16px;border-radius:12px}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:6px;font-size:.85rem;opacity:.8}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]{line-height:1.5;white-space:pre-wrap}.chat-main[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .message.loading[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]{padding:8px 0}.chat-main[_ngcontent-%COMP%]   .message-input-container[_ngcontent-%COMP%]{border-top:1px solid #e1e8ed;padding:20px;background:#fff}.chat-main[_ngcontent-%COMP%]   .message-input-container[_ngcontent-%COMP%]   .message-input-wrapper[_ngcontent-%COMP%]{display:flex;gap:12px;align-items:flex-end}.chat-main[_ngcontent-%COMP%]   .message-input-container[_ngcontent-%COMP%]   .message-input-wrapper[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]{flex:1;border:1px solid #ddd;border-radius:8px;padding:12px;resize:none;font-family:inherit;font-size:1rem;line-height:1.4;max-height:120px}.chat-main[_ngcontent-%COMP%]   .message-input-container[_ngcontent-%COMP%]   .message-input-wrapper[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#3498db}.chat-main[_ngcontent-%COMP%]   .message-input-container[_ngcontent-%COMP%]   .message-input-wrapper[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]:disabled{background:#f8f9fa;opacity:.6}.chat-main[_ngcontent-%COMP%]   .message-input-container[_ngcontent-%COMP%]   .message-input-wrapper[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%]{background:#3498db;color:#fff;border:none;border-radius:8px;padding:12px 16px;cursor:pointer;display:flex;align-items:center;justify-content:center}.chat-main[_ngcontent-%COMP%]   .message-input-container[_ngcontent-%COMP%]   .message-input-wrapper[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%]:hover:not(:disabled){background:#2980b9}.chat-main[_ngcontent-%COMP%]   .message-input-container[_ngcontent-%COMP%]   .message-input-wrapper[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%]:disabled{background:#bdc3c7;cursor:not-allowed}.chat-main[_ngcontent-%COMP%]   .message-input-container[_ngcontent-%COMP%]   .input-footer[_ngcontent-%COMP%]{margin-top:8px;text-align:center}.chat-main[_ngcontent-%COMP%]   .message-input-container[_ngcontent-%COMP%]   .input-footer[_ngcontent-%COMP%]   .input-hint[_ngcontent-%COMP%]{font-size:.8rem;color:#666}.typing-indicator[_ngcontent-%COMP%]{display:flex;gap:4px;align-items:center}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:6px;height:6px;border-radius:50%;background:#bbb;animation:_ngcontent-%COMP%_typing 1.4s infinite ease-in-out}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}@keyframes _ngcontent-%COMP%_typing{0%,80%,to{transform:scale(.8);opacity:.5}40%{transform:scale(1);opacity:1}}.icon-plus[_ngcontent-%COMP%]:before{content:"+"}.icon-menu[_ngcontent-%COMP%]:before{content:"\\2630"}.icon-user[_ngcontent-%COMP%]:before{content:"\\1f464"}.icon-bot[_ngcontent-%COMP%]:before{content:"\\1f916"}.icon-send[_ngcontent-%COMP%]:before{content:"\\27a4"}.icon-trash[_ngcontent-%COMP%]:before{content:"\\1f5d1"}.icon-alert[_ngcontent-%COMP%]:before{content:"\\26a0"}.icon-brain[_ngcontent-%COMP%]:before{content:"\\1f9e0"}.icon-database[_ngcontent-%COMP%]:before{content:"\\1f4be"}.icon-realtime[_ngcontent-%COMP%]:before{content:"\\26a1"}@media (max-width: 768px){.sidebar[_ngcontent-%COMP%]{position:absolute;z-index:100;height:100%}.sidebar.hidden[_ngcontent-%COMP%]{transform:translate(-100%)}.chat-main[_ngcontent-%COMP%]{width:100%}.message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:85%}}']})};var lc=class e{title="MindsDB Chat Application";static \u0275fac=function(n){return new(n||e)};static \u0275cmp=_r({type:e,selectors:[["app-root"]],decls:2,vars:0,template:function(n,r){n&1&&fe(0,"app-chat")(1,"router-outlet")},dependencies:[ei,cc],encapsulation:2})};Td(lc,sD).catch(e=>console.error(e));
