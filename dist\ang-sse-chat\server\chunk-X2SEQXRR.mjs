import './polyfills.server.mjs';
var u=Object.defineProperty,v=Object.defineProperties;var w=Object.getOwnPropertyDescriptors;var m=Object.getOwnPropertySymbols,x=Object.getPrototypeOf,q=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,y=Reflect.get;var l=(a,b)=>(b=Symbol[a])?b:Symbol.for("Symbol."+a),z=a=>{throw TypeError(a)};var p=(a,b,c)=>b in a?u(a,b,{enumerable:!0,configurable:!0,writable:!0,value:c}):a[b]=c,A=(a,b)=>{for(var c in b||={})q.call(b,c)&&p(a,c,b[c]);if(m)for(var c of m(b))r.call(b,c)&&p(a,c,b[c]);return a},B=(a,b)=>v(a,w(b));var C=(a=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(a,{get:(b,c)=>(typeof require<"u"?require:b)[c]}):a)(function(a){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+a+'" is not supported')});var D=(a,b)=>{var c={};for(var d in a)q.call(a,d)&&b.indexOf(d)<0&&(c[d]=a[d]);if(a!=null&&m)for(var d of m(a))b.indexOf(d)<0&&r.call(a,d)&&(c[d]=a[d]);return c};var E=(a,b)=>()=>(b||a((b={exports:{}}).exports,b),b.exports);var F=(a,b,c)=>y(x(a),c,b);var G=(a,b,c)=>new Promise((d,g)=>{var e=f=>{try{i(c.next(f))}catch(j){g(j)}},h=f=>{try{i(c.throw(f))}catch(j){g(j)}},i=f=>f.done?d(f.value):Promise.resolve(f.value).then(e,h);i((c=c.apply(a,b)).next())}),s=function(a,b){this[0]=a,this[1]=b},H=(a,b,c)=>{var d=(h,i,f,j)=>{try{var n=c[h](i),o=(i=n.value)instanceof s,t=n.done;Promise.resolve(o?i[0]:i).then(k=>o?d(h==="return"?h:"next",i[1]?{done:k.done,value:k.value}:k,f,j):f({value:k,done:t})).catch(k=>d("throw",k,f,j))}catch(k){j(k)}},g=h=>e[h]=i=>new Promise((f,j)=>d(h,i,f,j)),e={};return c=c.apply(a,b),e[l("asyncIterator")]=()=>e,g("next"),g("throw"),g("return"),e},I=a=>{var b=a[l("asyncIterator")],c=!1,d,g={};return b==null?(b=a[l("iterator")](),d=e=>g[e]=h=>b[e](h)):(b=b.call(a),d=e=>g[e]=h=>{if(c){if(c=!1,e==="throw")throw h;return h}return c=!0,{done:!1,value:new s(new Promise(i=>{var f=b[e](h);f instanceof Object||z("Object expected"),i(f)}),1)}}),g[l("iterator")]=()=>g,d("next"),"throw"in b?d("throw"):g.throw=e=>{throw e},"return"in b&&d("return"),g},J=(a,b,c)=>(b=a[l("asyncIterator")])?b.call(a):(a=a[l("iterator")](),b={},c=(d,g)=>(g=a[d])&&(b[d]=e=>new Promise((h,i,f)=>(e=g.call(a,e),f=e.done,Promise.resolve(e.value).then(j=>h({value:j,done:f}),i)))),c("next"),c("return"),b);export{A as a,B as b,C as c,D as d,E as e,F as f,G as g,s as h,H as i,I as j,J as k};
