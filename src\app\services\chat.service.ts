import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable, Subject, combineLatest, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { MindsDBService, ChatMessage } from './mindsdb.service';
import { SSEClientService, SSEEvent } from './sse-client.service';

export interface ChatConversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatState {
  currentConversation: ChatConversation | null;
  conversations: ChatConversation[];
  isLoading: boolean;
  error: string | null;
  isConnected: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private initialState: ChatState = {
    currentConversation: null,
    conversations: [],
    isLoading: false,
    error: null,
    isConnected: false
  };

  private stateSubject = new BehaviorSubject<ChatState>(this.initialState);
  public state$ = this.stateSubject.asObservable();

  private messageSubject = new Subject<ChatMessage>();
  public newMessage$ = this.messageSubject.asObservable();

  private selectedModel = 'gpt-4'; // Default model
  private sseUrl = 'http://localhost:47334/api/sse/chat';

  constructor(
    private mindsdbService: MindsDBService,
    private sseService: SSEClientService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.initializeConnections();
    if (isPlatformBrowser(this.platformId)) {
      this.setupSSEListeners();
    }
  }

  /**
   * Initialize connections to MindsDB and SSE
   */
  private initializeConnections(): void {
    // Monitor MindsDB connection status
    this.mindsdbService.isConnected$.subscribe(connected => {
      this.updateState({ isConnected: connected });
    });

    // Only test connection in browser environment
    if (isPlatformBrowser(this.platformId)) {
      // Test MindsDB connection
      this.mindsdbService.testConnection().subscribe({
        next: () => {
          console.log('MindsDB connection established');
          this.loadConversations();
        },
        error: (error) => {
          console.error('Failed to connect to MindsDB:', error);
          this.updateState({ error: 'Failed to connect to MindsDB' });
        }
      });
    } else {
      // In SSR, create a default conversation
      this.createConversation();
    }
  }

  /**
   * Setup SSE event listeners
   */
  private setupSSEListeners(): void {
    // Connect to SSE endpoint
    this.sseService.connect(this.sseUrl).subscribe({
      next: (event: SSEEvent) => {
        this.handleSSEEvent(event);
      },
      error: (error) => {
        console.error('SSE connection error:', error);
        this.updateState({ error: 'Real-time connection failed' });
      }
    });

    // Monitor SSE connection status
    this.sseService.connectionStatus$.subscribe(status => {
      if (!status.connected && status.error) {
        console.warn('SSE disconnected:', status.error);
        // Attempt to reconnect
        setTimeout(() => {
          this.sseService.reconnect(this.sseUrl);
        }, 5000);
      }
    });
  }

  /**
   * Handle incoming SSE events
   */
  private handleSSEEvent(event: SSEEvent): void {
    switch (event.type) {
      case 'chat-response':
        this.handleChatResponse(event.data);
        break;
      case 'chat-error':
        this.updateState({ error: event.data.message, isLoading: false });
        break;
      case 'model-update':
        console.log('Model updated:', event.data);
        break;
      default:
        console.log('Unhandled SSE event:', event);
    }
  }

  /**
   * Handle chat response from SSE
   */
  private handleChatResponse(data: any): void {
    const message: ChatMessage = {
      id: this.generateMessageId(),
      content: data.response || data.content,
      role: 'assistant',
      timestamp: new Date()
    };

    this.addMessageToCurrentConversation(message);
    this.messageSubject.next(message);
    this.updateState({ isLoading: false });
  }

  /**
   * Send a message to the chat
   */
  sendMessage(content: string): Observable<ChatMessage> {
    const userMessage: ChatMessage = {
      id: this.generateMessageId(),
      content,
      role: 'user',
      timestamp: new Date()
    };

    // Add user message to conversation
    this.addMessageToCurrentConversation(userMessage);
    this.messageSubject.next(userMessage);
    this.updateState({ isLoading: true, error: null });

    // Query MindsDB model
    return this.mindsdbService.queryModel(this.selectedModel, content).pipe(
      map(response => {
        const assistantMessage: ChatMessage = {
          id: this.generateMessageId(),
          content: response,
          role: 'assistant',
          timestamp: new Date()
        };

        this.addMessageToCurrentConversation(assistantMessage);
        this.messageSubject.next(assistantMessage);
        this.updateState({ isLoading: false });

        return assistantMessage;
      }),
      catchError(error => {
        console.error('Error sending message:', error);
        this.updateState({ 
          error: 'Failed to send message: ' + error.message, 
          isLoading: false 
        });
        throw error;
      })
    );
  }

  /**
   * Create a new conversation
   */
  createConversation(title?: string): ChatConversation {
    const conversation: ChatConversation = {
      id: this.generateConversationId(),
      title: title || `Chat ${new Date().toLocaleString()}`,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const currentState = this.stateSubject.value;
    const updatedConversations = [conversation, ...currentState.conversations];

    this.updateState({
      currentConversation: conversation,
      conversations: updatedConversations
    });

    return conversation;
  }

  /**
   * Switch to a different conversation
   */
  switchConversation(conversationId: string): void {
    const currentState = this.stateSubject.value;
    const conversation = currentState.conversations.find(c => c.id === conversationId);
    
    if (conversation) {
      this.updateState({ currentConversation: conversation });
    }
  }

  /**
   * Delete a conversation
   */
  deleteConversation(conversationId: string): void {
    const currentState = this.stateSubject.value;
    const updatedConversations = currentState.conversations.filter(c => c.id !== conversationId);
    
    let newCurrentConversation = currentState.currentConversation;
    if (currentState.currentConversation?.id === conversationId) {
      newCurrentConversation = updatedConversations[0] || null;
    }

    this.updateState({
      conversations: updatedConversations,
      currentConversation: newCurrentConversation
    });
  }

  /**
   * Set the selected model
   */
  setModel(modelName: string): void {
    this.selectedModel = modelName;
  }

  /**
   * Get available models
   */
  getAvailableModels(): Observable<string[]> {
    return this.mindsdbService.getModels();
  }

  /**
   * Clear current conversation
   */
  clearCurrentConversation(): void {
    const currentState = this.stateSubject.value;
    if (currentState.currentConversation) {
      const clearedConversation = {
        ...currentState.currentConversation,
        messages: [],
        updatedAt: new Date()
      };
      this.updateState({ currentConversation: clearedConversation });
    }
  }

  private addMessageToCurrentConversation(message: ChatMessage): void {
    const currentState = this.stateSubject.value;
    if (!currentState.currentConversation) {
      this.createConversation();
    }

    const updatedConversation = {
      ...currentState.currentConversation!,
      messages: [...currentState.currentConversation!.messages, message],
      updatedAt: new Date()
    };

    const updatedConversations = currentState.conversations.map(c =>
      c.id === updatedConversation.id ? updatedConversation : c
    );

    this.updateState({
      currentConversation: updatedConversation,
      conversations: updatedConversations
    });

    // Save to MindsDB (optional)
    this.mindsdbService.saveChatMessage(message, updatedConversation.id).subscribe({
      error: (error) => console.warn('Failed to save message to MindsDB:', error)
    });
  }

  private loadConversations(): void {
    this.mindsdbService.getChatHistory().subscribe({
      next: (messages) => {
        // Group messages by conversation (simplified)
        if (messages.length > 0) {
          const conversation = this.createConversation('Loaded Chat');
          conversation.messages = messages.reverse();
          this.updateState({ currentConversation: conversation });
        }
      },
      error: (error) => {
        console.warn('Failed to load chat history:', error);
        // Create a new conversation if loading fails
        this.createConversation();
      }
    });
  }

  private updateState(partialState: Partial<ChatState>): void {
    const currentState = this.stateSubject.value;
    this.stateSubject.next({ ...currentState, ...partialState });
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateConversationId(): string {
    return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
