{"version": 3, "file": "testing.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/platform-server/testing/src/server.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  createPlatformFactory,\n  NgModule,\n  platformCore,\n  PlatformRef,\n  StaticProvider,\n} from '@angular/core';\nimport {ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS} from '@angular/platform-browser-dynamic';\nimport {BrowserDynamicTestingModule} from '@angular/platform-browser-dynamic/testing';\nimport {\n  ɵINTERNAL_SERVER_PLATFORM_PROVIDERS as INTERNAL_SERVER_PLATFORM_PROVIDERS,\n  ɵSERVER_RENDER_PROVIDERS as SERVER_RENDER_PROVIDERS,\n} from '../../index';\n\nconst INTERNAL_SERVER_DYNAMIC_PLATFORM_TESTING_PROVIDERS: StaticProvider[] = [\n  ...INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS,\n  ...INTERNAL_SERVER_PLATFORM_PROVIDERS,\n];\n\n/**\n * Platform for testing\n *\n * @publicApi\n */\nexport const platformServerTesting: (extraProviders?: StaticProvider[]) => PlatformRef =\n  createPlatformFactory(\n    platformCore,\n    'serverTesting',\n    INTERNAL_SERVER_DYNAMIC_PLATFORM_TESTING_PROVIDERS,\n  );\n\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\n@NgModule({\n  exports: [BrowserDynamicTestingModule],\n  providers: SERVER_RENDER_PROVIDERS,\n})\nexport class ServerTestingModule {}\n"], "names": ["INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS"], "mappings": ";;;;;;;;;;;;;;;;AAsBA,MAAM,kDAAkD,GAAqB;AAC3E,IAAA,GAAGA,4CAA2C;AAC9C,IAAA,GAAG,kCAAkC;CACtC;AAED;;;;AAIG;AACI,MAAM,qBAAqB,GAChC,qBAAqB,CACnB,YAAY,EACZ,eAAe,EACf,kDAAkD;AAGtD;;;;AAIG;MAKU,mBAAmB,CAAA;kHAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,YAHpB,2BAA2B,CAAA,EAAA,CAAA;mHAG1B,mBAAmB,EAAA,SAAA,EAFnB,uBAAuB,EAAA,OAAA,EAAA,CADxB,2BAA2B,CAAA,EAAA,CAAA;;sGAG1B,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAJ/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,2BAA2B,CAAC;AACtC,oBAAA,SAAS,EAAE,uBAAuB;AACnC,iBAAA;;;;;"}