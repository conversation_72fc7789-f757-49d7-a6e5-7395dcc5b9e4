<!DOCTYPE html><html lang="en" data-beasties-container><head>
  <meta charset="utf-8">
  <title>AngSseChat</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
<style>*{box-sizing:border-box}html,body{margin:0;padding:0;height:100%;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif;background:#f5f5f5}body{overflow:hidden}h1,h2,h3{margin:0;font-weight:600}p{margin:0}button{font-family:inherit;cursor:pointer;border:none;outline:none}button:focus-visible{outline:2px solid #3498db;outline-offset:2px}textarea,select{font-family:inherit;outline:none}textarea:focus,select:focus{outline:2px solid #3498db;outline-offset:1px}
</style><link rel="stylesheet" href="styles-Z73UOTI6.css" media="print" onload="this.media='all'"><noscript><link rel="stylesheet" href="styles-Z73UOTI6.css"></noscript><style ng-app-id="ng">@charset "UTF-8";.chat-container[_ngcontent-ng-c2766820189]{display:flex;height:100vh;background:#f5f5f5;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif}.sidebar[_ngcontent-ng-c2766820189]{width:300px;background:#2c3e50;color:#fff;display:flex;flex-direction:column;transition:transform .3s ease}.sidebar.hidden[_ngcontent-ng-c2766820189]{transform:translate(-100%);width:0}.sidebar[_ngcontent-ng-c2766820189]   .sidebar-header[_ngcontent-ng-c2766820189]{padding:20px;border-bottom:1px solid #34495e}.sidebar[_ngcontent-ng-c2766820189]   .sidebar-header[_ngcontent-ng-c2766820189]   h2[_ngcontent-ng-c2766820189]{margin:0 0 15px;font-size:1.5rem;font-weight:600}.sidebar[_ngcontent-ng-c2766820189]   .sidebar-header[_ngcontent-ng-c2766820189]   .btn[_ngcontent-ng-c2766820189]{width:100%;padding:10px;border:none;border-radius:6px;cursor:pointer;font-weight:500;display:flex;align-items:center;justify-content:center;gap:8px}.sidebar[_ngcontent-ng-c2766820189]   .sidebar-header[_ngcontent-ng-c2766820189]   .btn.btn-primary[_ngcontent-ng-c2766820189]{background:#3498db;color:#fff}.sidebar[_ngcontent-ng-c2766820189]   .sidebar-header[_ngcontent-ng-c2766820189]   .btn.btn-primary[_ngcontent-ng-c2766820189]:hover{background:#2980b9}.sidebar[_ngcontent-ng-c2766820189]   .connection-status[_ngcontent-ng-c2766820189]{padding:15px 20px;display:flex;align-items:center;gap:10px;border-bottom:1px solid #34495e}.sidebar[_ngcontent-ng-c2766820189]   .connection-status[_ngcontent-ng-c2766820189]   .status-indicator[_ngcontent-ng-c2766820189]{width:8px;height:8px;border-radius:50%;background:#e74c3c}.sidebar[_ngcontent-ng-c2766820189]   .connection-status.connected[_ngcontent-ng-c2766820189]   .status-indicator[_ngcontent-ng-c2766820189]{background:#27ae60}.sidebar[_ngcontent-ng-c2766820189]   .model-selection[_ngcontent-ng-c2766820189]{padding:15px 20px;border-bottom:1px solid #34495e}.sidebar[_ngcontent-ng-c2766820189]   .model-selection[_ngcontent-ng-c2766820189]   label[_ngcontent-ng-c2766820189]{display:block;margin-bottom:8px;font-weight:500}.sidebar[_ngcontent-ng-c2766820189]   .model-selection[_ngcontent-ng-c2766820189]   .model-select[_ngcontent-ng-c2766820189]{width:100%;padding:8px;border:1px solid #34495e;border-radius:4px;background:#34495e;color:#fff}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]{flex:1;overflow-y:auto}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   h3[_ngcontent-ng-c2766820189]{padding:15px 20px 10px;margin:0;font-size:1rem;font-weight:600;border-bottom:1px solid #34495e}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   .conversation-items[_ngcontent-ng-c2766820189]{padding:10px 0}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   .conversation-item[_ngcontent-ng-c2766820189]{padding:12px 20px;cursor:pointer;border-bottom:1px solid #34495e;display:flex;align-items:center;justify-content:space-between;transition:background .2s}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   .conversation-item[_ngcontent-ng-c2766820189]:hover{background:#34495e}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   .conversation-item.active[_ngcontent-ng-c2766820189]{background:#3498db}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   .conversation-item[_ngcontent-ng-c2766820189]   .conversation-content[_ngcontent-ng-c2766820189]{flex:1;min-width:0}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   .conversation-item[_ngcontent-ng-c2766820189]   .conversation-content[_ngcontent-ng-c2766820189]   .conversation-title[_ngcontent-ng-c2766820189]{font-weight:500;margin-bottom:4px}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   .conversation-item[_ngcontent-ng-c2766820189]   .conversation-content[_ngcontent-ng-c2766820189]   .conversation-preview[_ngcontent-ng-c2766820189]{font-size:.85rem;opacity:.7;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   .conversation-item[_ngcontent-ng-c2766820189]   .conversation-content[_ngcontent-ng-c2766820189]   .conversation-time[_ngcontent-ng-c2766820189]{font-size:.75rem;opacity:.5;margin-top:4px}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   .conversation-item[_ngcontent-ng-c2766820189]   .delete-btn[_ngcontent-ng-c2766820189]{background:none;border:none;color:#e74c3c;cursor:pointer;padding:4px;border-radius:4px;opacity:0;transition:opacity .2s}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   .conversation-item[_ngcontent-ng-c2766820189]   .delete-btn[_ngcontent-ng-c2766820189]:hover{background:#e74c3c;color:#fff}.sidebar[_ngcontent-ng-c2766820189]   .conversations-list[_ngcontent-ng-c2766820189]   .conversation-item[_ngcontent-ng-c2766820189]:hover   .delete-btn[_ngcontent-ng-c2766820189]{opacity:1}.chat-main[_ngcontent-ng-c2766820189]{flex:1;display:flex;flex-direction:column;background:#fff}.chat-main.full-width[_ngcontent-ng-c2766820189]{width:100%}.chat-main[_ngcontent-ng-c2766820189]   .chat-header[_ngcontent-ng-c2766820189]{padding:15px 20px;border-bottom:1px solid #e1e8ed;display:flex;align-items:center;gap:15px;background:#fff}.chat-main[_ngcontent-ng-c2766820189]   .chat-header[_ngcontent-ng-c2766820189]   .sidebar-toggle[_ngcontent-ng-c2766820189]{background:none;border:none;font-size:1.2rem;cursor:pointer;padding:8px;border-radius:4px}.chat-main[_ngcontent-ng-c2766820189]   .chat-header[_ngcontent-ng-c2766820189]   .sidebar-toggle[_ngcontent-ng-c2766820189]:hover{background:#f8f9fa}.chat-main[_ngcontent-ng-c2766820189]   .chat-header[_ngcontent-ng-c2766820189]   .chat-title[_ngcontent-ng-c2766820189]{flex:1}.chat-main[_ngcontent-ng-c2766820189]   .chat-header[_ngcontent-ng-c2766820189]   .chat-title[_ngcontent-ng-c2766820189]   h1[_ngcontent-ng-c2766820189]{margin:0;font-size:1.3rem;font-weight:600}.chat-main[_ngcontent-ng-c2766820189]   .chat-header[_ngcontent-ng-c2766820189]   .chat-title[_ngcontent-ng-c2766820189]   .model-indicator[_ngcontent-ng-c2766820189]{font-size:.85rem;color:#666}.chat-main[_ngcontent-ng-c2766820189]   .chat-header[_ngcontent-ng-c2766820189]   .chat-actions[_ngcontent-ng-c2766820189]   .btn[_ngcontent-ng-c2766820189]{padding:8px 16px;border:1px solid #ddd;border-radius:4px;background:#fff;cursor:pointer}.chat-main[_ngcontent-ng-c2766820189]   .chat-header[_ngcontent-ng-c2766820189]   .chat-actions[_ngcontent-ng-c2766820189]   .btn[_ngcontent-ng-c2766820189]:hover:not(:disabled){background:#f8f9fa}.chat-main[_ngcontent-ng-c2766820189]   .chat-header[_ngcontent-ng-c2766820189]   .chat-actions[_ngcontent-ng-c2766820189]   .btn[_ngcontent-ng-c2766820189]:disabled{opacity:.5;cursor:not-allowed}.chat-main[_ngcontent-ng-c2766820189]   .error-banner[_ngcontent-ng-c2766820189]{background:#fee;color:#c33;padding:12px 20px;display:flex;align-items:center;gap:10px;border-bottom:1px solid #fcc}.chat-main[_ngcontent-ng-c2766820189]   .error-banner[_ngcontent-ng-c2766820189]   .retry-btn[_ngcontent-ng-c2766820189]{background:#c33;color:#fff;border:none;padding:4px 12px;border-radius:4px;cursor:pointer;font-size:.85rem}.chat-main[_ngcontent-ng-c2766820189]   .error-banner[_ngcontent-ng-c2766820189]   .retry-btn[_ngcontent-ng-c2766820189]:hover{background:#a22}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]{flex:1;overflow-y:auto;padding:20px}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .welcome-message[_ngcontent-ng-c2766820189]{text-align:center;padding:60px 20px}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .welcome-message[_ngcontent-ng-c2766820189]   .welcome-content[_ngcontent-ng-c2766820189]{max-width:500px;margin:0 auto}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .welcome-message[_ngcontent-ng-c2766820189]   .welcome-content[_ngcontent-ng-c2766820189]   h2[_ngcontent-ng-c2766820189]{margin-bottom:15px;color:#2c3e50}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .welcome-message[_ngcontent-ng-c2766820189]   .welcome-content[_ngcontent-ng-c2766820189]   p[_ngcontent-ng-c2766820189]{color:#666;margin-bottom:30px}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .welcome-message[_ngcontent-ng-c2766820189]   .welcome-content[_ngcontent-ng-c2766820189]   .welcome-features[_ngcontent-ng-c2766820189]{display:flex;justify-content:center;gap:30px;flex-wrap:wrap}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .welcome-message[_ngcontent-ng-c2766820189]   .welcome-content[_ngcontent-ng-c2766820189]   .welcome-features[_ngcontent-ng-c2766820189]   .feature[_ngcontent-ng-c2766820189]{display:flex;align-items:center;gap:8px;color:#3498db;font-weight:500}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .message[_ngcontent-ng-c2766820189]{display:flex;gap:12px;margin-bottom:20px}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .message.user-message[_ngcontent-ng-c2766820189]{flex-direction:row-reverse}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .message.user-message[_ngcontent-ng-c2766820189]   .message-content[_ngcontent-ng-c2766820189]{background:#3498db;color:#fff}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .message.assistant-message[_ngcontent-ng-c2766820189]   .message-content[_ngcontent-ng-c2766820189]{background:#f8f9fa;border:1px solid #e1e8ed}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .message[_ngcontent-ng-c2766820189]   .message-avatar[_ngcontent-ng-c2766820189]{width:36px;height:36px;border-radius:50%;background:#ddd;display:flex;align-items:center;justify-content:center;flex-shrink:0}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .message[_ngcontent-ng-c2766820189]   .message-content[_ngcontent-ng-c2766820189]{max-width:70%;padding:12px 16px;border-radius:12px}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .message[_ngcontent-ng-c2766820189]   .message-content[_ngcontent-ng-c2766820189]   .message-header[_ngcontent-ng-c2766820189]{display:flex;justify-content:space-between;align-items:center;margin-bottom:6px;font-size:.85rem;opacity:.8}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .message[_ngcontent-ng-c2766820189]   .message-content[_ngcontent-ng-c2766820189]   .message-text[_ngcontent-ng-c2766820189]{line-height:1.5;white-space:pre-wrap}.chat-main[_ngcontent-ng-c2766820189]   .messages-container[_ngcontent-ng-c2766820189]   .message.loading[_ngcontent-ng-c2766820189]   .message-text[_ngcontent-ng-c2766820189]{padding:8px 0}.chat-main[_ngcontent-ng-c2766820189]   .message-input-container[_ngcontent-ng-c2766820189]{border-top:1px solid #e1e8ed;padding:20px;background:#fff}.chat-main[_ngcontent-ng-c2766820189]   .message-input-container[_ngcontent-ng-c2766820189]   .message-input-wrapper[_ngcontent-ng-c2766820189]{display:flex;gap:12px;align-items:flex-end}.chat-main[_ngcontent-ng-c2766820189]   .message-input-container[_ngcontent-ng-c2766820189]   .message-input-wrapper[_ngcontent-ng-c2766820189]   .message-input[_ngcontent-ng-c2766820189]{flex:1;border:1px solid #ddd;border-radius:8px;padding:12px;resize:none;font-family:inherit;font-size:1rem;line-height:1.4;max-height:120px}.chat-main[_ngcontent-ng-c2766820189]   .message-input-container[_ngcontent-ng-c2766820189]   .message-input-wrapper[_ngcontent-ng-c2766820189]   .message-input[_ngcontent-ng-c2766820189]:focus{outline:none;border-color:#3498db}.chat-main[_ngcontent-ng-c2766820189]   .message-input-container[_ngcontent-ng-c2766820189]   .message-input-wrapper[_ngcontent-ng-c2766820189]   .message-input[_ngcontent-ng-c2766820189]:disabled{background:#f8f9fa;opacity:.6}.chat-main[_ngcontent-ng-c2766820189]   .message-input-container[_ngcontent-ng-c2766820189]   .message-input-wrapper[_ngcontent-ng-c2766820189]   .send-button[_ngcontent-ng-c2766820189]{background:#3498db;color:#fff;border:none;border-radius:8px;padding:12px 16px;cursor:pointer;display:flex;align-items:center;justify-content:center}.chat-main[_ngcontent-ng-c2766820189]   .message-input-container[_ngcontent-ng-c2766820189]   .message-input-wrapper[_ngcontent-ng-c2766820189]   .send-button[_ngcontent-ng-c2766820189]:hover:not(:disabled){background:#2980b9}.chat-main[_ngcontent-ng-c2766820189]   .message-input-container[_ngcontent-ng-c2766820189]   .message-input-wrapper[_ngcontent-ng-c2766820189]   .send-button[_ngcontent-ng-c2766820189]:disabled{background:#bdc3c7;cursor:not-allowed}.chat-main[_ngcontent-ng-c2766820189]   .message-input-container[_ngcontent-ng-c2766820189]   .input-footer[_ngcontent-ng-c2766820189]{margin-top:8px;text-align:center}.chat-main[_ngcontent-ng-c2766820189]   .message-input-container[_ngcontent-ng-c2766820189]   .input-footer[_ngcontent-ng-c2766820189]   .input-hint[_ngcontent-ng-c2766820189]{font-size:.8rem;color:#666}.typing-indicator[_ngcontent-ng-c2766820189]{display:flex;gap:4px;align-items:center}.typing-indicator[_ngcontent-ng-c2766820189]   span[_ngcontent-ng-c2766820189]{width:6px;height:6px;border-radius:50%;background:#bbb;animation:_ngcontent-ng-c2766820189_typing 1.4s infinite ease-in-out}.typing-indicator[_ngcontent-ng-c2766820189]   span[_ngcontent-ng-c2766820189]:nth-child(1){animation-delay:-.32s}.typing-indicator[_ngcontent-ng-c2766820189]   span[_ngcontent-ng-c2766820189]:nth-child(2){animation-delay:-.16s}@keyframes _ngcontent-ng-c2766820189_typing{0%,80%,to{transform:scale(.8);opacity:.5}40%{transform:scale(1);opacity:1}}.icon-plus[_ngcontent-ng-c2766820189]:before{content:"+"}.icon-menu[_ngcontent-ng-c2766820189]:before{content:"\2630"}.icon-user[_ngcontent-ng-c2766820189]:before{content:"\1f464"}.icon-bot[_ngcontent-ng-c2766820189]:before{content:"\1f916"}.icon-send[_ngcontent-ng-c2766820189]:before{content:"\27a4"}.icon-trash[_ngcontent-ng-c2766820189]:before{content:"\1f5d1"}.icon-alert[_ngcontent-ng-c2766820189]:before{content:"\26a0"}.icon-brain[_ngcontent-ng-c2766820189]:before{content:"\1f9e0"}.icon-database[_ngcontent-ng-c2766820189]:before{content:"\1f4be"}.icon-realtime[_ngcontent-ng-c2766820189]:before{content:"\26a1"}@media (max-width: 768px){.sidebar[_ngcontent-ng-c2766820189]{position:absolute;z-index:100;height:100%}.sidebar.hidden[_ngcontent-ng-c2766820189]{transform:translate(-100%)}.chat-main[_ngcontent-ng-c2766820189]{width:100%}.message[_ngcontent-ng-c2766820189]   .message-content[_ngcontent-ng-c2766820189]{max-width:85%}}</style></head>
<body><!--nghm--><script type="text/javascript" id="ng-event-dispatch-contract">(()=>{function p(t,n,r,o,e,i,f,m){return{eventType:t,event:n,targetElement:r,eic:o,timeStamp:e,eia:i,eirp:f,eiack:m}}function u(t){let n=[],r=e=>{n.push(e)};return{c:t,q:n,et:[],etc:[],d:r,h:e=>{r(p(e.type,e,e.target,t,Date.now()))}}}function s(t,n,r){for(let o=0;o<n.length;o++){let e=n[o];(r?t.etc:t.et).push(e),t.c.addEventListener(e,t.h,r)}}function c(t,n,r,o,e=window){let i=u(t);e._ejsas||(e._ejsas={}),e._ejsas[n]=i,s(i,r),s(i,o,!0)}window.__jsaction_bootstrap=c;})();
</script><script>window.__jsaction_bootstrap(document.body,"ng",["click","change","input","compositionstart","compositionend","keydown"],["blur"]);</script>
  <app-root ng-version="19.2.14" ngh="1" ng-server-context="ssg"><app-chat _nghost-ng-c2766820189 ngh="0"><div _ngcontent-ng-c2766820189 class="chat-container"><div _ngcontent-ng-c2766820189 class="sidebar"><div _ngcontent-ng-c2766820189 class="sidebar-header"><h2 _ngcontent-ng-c2766820189>MindsDB Chat</h2><button _ngcontent-ng-c2766820189 class="btn btn-primary" jsaction="click:;"><i _ngcontent-ng-c2766820189 class="icon-plus"></i> New Chat </button></div><div _ngcontent-ng-c2766820189 class="connection-status"><div _ngcontent-ng-c2766820189 class="status-indicator"></div><span _ngcontent-ng-c2766820189>Disconnected</span></div><div _ngcontent-ng-c2766820189 class="model-selection"><label _ngcontent-ng-c2766820189 for="model-select">Model:</label><select _ngcontent-ng-c2766820189 id="model-select" class="model-select ng-untouched ng-pristine ng-valid" jsaction="change:;blur:;"><option _ngcontent-ng-c2766820189 value="gpt-4"> gpt-4 </option><option _ngcontent-ng-c2766820189 value="gpt-3.5-turbo"> gpt-3.5-turbo </option><option _ngcontent-ng-c2766820189 value="claude-3"> claude-3 </option><!----></select></div><div _ngcontent-ng-c2766820189 class="conversations-list"><h3 _ngcontent-ng-c2766820189>Conversations</h3><div _ngcontent-ng-c2766820189 class="conversation-items"><div _ngcontent-ng-c2766820189 class="conversation-item active" jsaction="click:;"><div _ngcontent-ng-c2766820189 class="conversation-content"><div _ngcontent-ng-c2766820189 class="conversation-title">Chat 20/8/2025, 3:21:09 pm</div><div _ngcontent-ng-c2766820189 class="conversation-preview">New conversation</div><div _ngcontent-ng-c2766820189 class="conversation-time">8/20/25, 3:21 PM</div></div><button _ngcontent-ng-c2766820189 title="Delete conversation" class="delete-btn" jsaction="click:;"><i _ngcontent-ng-c2766820189 class="icon-trash"></i></button></div><!----></div></div></div><div _ngcontent-ng-c2766820189 class="chat-main"><div _ngcontent-ng-c2766820189 class="chat-header"><button _ngcontent-ng-c2766820189 class="sidebar-toggle" jsaction="click:;"><i _ngcontent-ng-c2766820189 class="icon-menu"></i></button><div _ngcontent-ng-c2766820189 class="chat-title"><h1 _ngcontent-ng-c2766820189>Chat 20/8/2025, 3:21:09 pm</h1><span _ngcontent-ng-c2766820189 class="model-indicator">Using: gpt-4</span></div><div _ngcontent-ng-c2766820189 class="chat-actions"><button _ngcontent-ng-c2766820189 class="btn btn-secondary" disabled jsaction="click:;"> Clear Chat </button></div></div><!----><div _ngcontent-ng-c2766820189 class="messages-container"><div _ngcontent-ng-c2766820189 class="messages-list"><div _ngcontent-ng-c2766820189 class="welcome-message"><div _ngcontent-ng-c2766820189 class="welcome-content"><h2 _ngcontent-ng-c2766820189>Welcome to MindsDB Chat</h2><p _ngcontent-ng-c2766820189>Start a conversation with your AI assistant powered by MindsDB.</p><div _ngcontent-ng-c2766820189 class="welcome-features"><div _ngcontent-ng-c2766820189 class="feature"><i _ngcontent-ng-c2766820189 class="icon-brain"></i><span _ngcontent-ng-c2766820189>AI-powered responses</span></div><div _ngcontent-ng-c2766820189 class="feature"><i _ngcontent-ng-c2766820189 class="icon-database"></i><span _ngcontent-ng-c2766820189>Connected to MindsDB</span></div><div _ngcontent-ng-c2766820189 class="feature"><i _ngcontent-ng-c2766820189 class="icon-realtime"></i><span _ngcontent-ng-c2766820189>Real-time communication</span></div></div></div></div><!----><!----><!----></div></div><div _ngcontent-ng-c2766820189 class="message-input-container"><div _ngcontent-ng-c2766820189 class="message-input-wrapper"><textarea _ngcontent-ng-c2766820189 placeholder="Type your message here... (Press Enter to send, Shift+Enter for new line)" rows="1" class="message-input ng-untouched ng-pristine" disabled jsaction="input:;blur:;compositionstart:;compositionend:;keydown:;"></textarea><button _ngcontent-ng-c2766820189 class="send-button" disabled jsaction="click:;"><i _ngcontent-ng-c2766820189 class="icon-send"></i></button></div><div _ngcontent-ng-c2766820189 class="input-footer"><span _ngcontent-ng-c2766820189 class="input-hint"> Connecting... </span></div></div></div></div></app-chat><router-outlet></router-outlet><!----></app-root>
<script src="polyfills-B6TNHZQ6.js" type="module"></script><script src="main-THI55BKF.js" type="module"></script>

<script id="ng-state" type="application/json">{"__nghData__":[{"t":{"16":"t0","21":"t1","34":"t2","38":"t3","39":"t4","40":"t5"},"c":{"16":[{"i":"t0","r":1,"x":3}],"21":[{"i":"t1","r":1}],"34":[],"38":[{"i":"t3","r":1}],"39":[],"40":[]}},{"c":{"1":[]}}]}</script></body></html>